<!doctype html>
<!-- ✨ Built with Framer • https://www.framer.com/ -->
<html lang="en" dir="ltr">

<!-- Mirrored from section43realty.framer.website/ by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 09 Oct 2025 19:33:05 GMT -->

<head>
    <meta charset="utf-8">


    <script>try { if (localStorage.get("__framer_force_showing_editorbar_since")) { const n = document.createElement("link"); n.rel = "modulepreload"; n.href = "https://framer.com/edit/init.mjs"; document.head.appendChild(n) } } catch (e) { }</script>
    <!-- Start of headStart -->

    <!-- End of headStart -->
    <meta name="viewport" content="width=1519">
    <meta name="generator" content="Framer d800442">
    <title>Section43Realty</title>
    <meta name="description" content="Making land ownership in Nigeria transparent, safe, and accessible for everyone.">
    <meta name="framer-search-index"
        content="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/searchIndex-oSBHCNv827qQ.json">
    <meta name="framer-search-index-fallback"
        content="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/searchIndex-XgK7sWWPUbgu.json">
    <link href="../framerusercontent.com/sites/icons/default-favicon-light.v1.png" rel="icon"
        media="(prefers-color-scheme: light)">
    <link href="../framerusercontent.com/sites/icons/default-favicon-dark.v1.png" rel="icon"
        media="(prefers-color-scheme: dark)">
    <link rel="apple-touch-icon" href="../framerusercontent.com/sites/icons/default-touch-icon.v3.png">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Section43Realty">
    <meta property="og:description"
        content="Making land ownership in Nigeria transparent, safe, and accessible for everyone.">
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Section43Realty">
    <meta name="twitter:description"
        content="Making land ownership in Nigeria transparent, safe, and accessible for everyone.">

    <style data-framer-font-css></style>

    <meta name="robots" content="max-image-preview:large">
    <link rel="canonical" href="index.html">
    <meta property="og:url" content="https://section43realty.framer.website/">
    <style data-framer-css-ssr-minified data-framer-components="framer-lib-cursors-host framer-zh2Qm">
        html,
        body,
        #main {
            box-sizing: border-box;
            margin: 0;
            padding: 0
        }

        :root {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale
        }

        * {
            box-sizing: border-box;
            -webkit-font-smoothing: inherit
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        figure {
            margin: 0
        }

        body,
        input,
        textarea,
        select,
        button {
            font-family: sans-serif;
            font-size: 12px
        }

        @supports (z-index:calc(infinity)) {
            #__framer-badge-container {
                --infinity: infinity
            }
        }

        #__framer-badge-container {
            pointer-events: none;
            width: 100%;
            z-index: calc(var(--infinity, 2147480000));
            justify-content: flex-end;
            padding: 20px;
            display: flex;
            position: fixed;
            bottom: 0
        }

        body {
            --framer-will-change-override: none
        }

        @supports (background:-webkit-named-image(i)) and (not (grid-template-rows:subgrid)) {
            body {
                --framer-will-change-override: transform
            }
        }

        [data-framer-component-type] {
            position: absolute
        }

        [data-framer-component-type=Text] {
            cursor: inherit
        }

        [data-framer-component-text-autosized] * {
            white-space: pre
        }

        [data-framer-component-type=Text]>* {
            text-align: var(--framer-text-alignment, start)
        }

        [data-framer-component-type=Text] span span,
        [data-framer-component-type=Text] p span,
        [data-framer-component-type=Text] h1 span,
        [data-framer-component-type=Text] h2 span,
        [data-framer-component-type=Text] h3 span,
        [data-framer-component-type=Text] h4 span,
        [data-framer-component-type=Text] h5 span,
        [data-framer-component-type=Text] h6 span {
            display: block
        }

        [data-framer-component-type=Text] span span span,
        [data-framer-component-type=Text] p span span,
        [data-framer-component-type=Text] h1 span span,
        [data-framer-component-type=Text] h2 span span,
        [data-framer-component-type=Text] h3 span span,
        [data-framer-component-type=Text] h4 span span,
        [data-framer-component-type=Text] h5 span span,
        [data-framer-component-type=Text] h6 span span {
            display: unset
        }

        [data-framer-component-type=Text] div div span,
        [data-framer-component-type=Text] a div span,
        [data-framer-component-type=Text] span span span,
        [data-framer-component-type=Text] p span span,
        [data-framer-component-type=Text] h1 span span,
        [data-framer-component-type=Text] h2 span span,
        [data-framer-component-type=Text] h3 span span,
        [data-framer-component-type=Text] h4 span span,
        [data-framer-component-type=Text] h5 span span,
        [data-framer-component-type=Text] h6 span span,
        [data-framer-component-type=Text] a {
            font-family: var(--font-family);
            font-style: var(--font-style);
            font-weight: min(calc(var(--framer-font-weight-increase, 0) + var(--font-weight, 400)), 900);
            color: var(--text-color);
            letter-spacing: var(--letter-spacing);
            font-size: var(--font-size);
            text-transform: var(--text-transform);
            --text-decoration: var(--framer-text-decoration-style, solid)var(--framer-text-decoration, none)var(--framer-text-decoration-color, currentcolor)var(--framer-text-decoration-thickness, auto);
            --text-decoration-skip-ink: var(--framer-text-decoration-skip-ink);
            --text-underline-offset: var(--framer-text-decoration-offset);
            line-height: var(--line-height);
            --font-family: var(--framer-font-family);
            --font-style: var(--framer-font-style);
            --font-weight: var(--framer-font-weight);
            --text-color: var(--framer-text-color);
            --letter-spacing: var(--framer-letter-spacing);
            --font-size: var(--framer-font-size);
            --text-transform: var(--framer-text-transform);
            --line-height: var(--framer-line-height)
        }

        [data-framer-component-type=Text] a,
        [data-framer-component-type=Text] a div span,
        [data-framer-component-type=Text] a span span span,
        [data-framer-component-type=Text] a p span span,
        [data-framer-component-type=Text] a h1 span span,
        [data-framer-component-type=Text] a h2 span span,
        [data-framer-component-type=Text] a h3 span span,
        [data-framer-component-type=Text] a h4 span span,
        [data-framer-component-type=Text] a h5 span span,
        [data-framer-component-type=Text] a h6 span span {
            --font-family: var(--framer-link-font-family, var(--framer-font-family));
            --font-style: var(--framer-link-font-style, var(--framer-font-style));
            --font-weight: var(--framer-link-font-weight, var(--framer-font-weight));
            --text-color: var(--framer-link-text-color, var(--framer-text-color));
            --font-size: var(--framer-link-font-size, var(--framer-font-size));
            --text-transform: var(--framer-link-text-transform, var(--framer-text-transform));
            --text-decoration: var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, solid))var(--framer-link-text-decoration, var(--framer-text-decoration, none))var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, currentcolor))var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, auto));
            --text-decoration-skip-ink: var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink));
            --text-underline-offset: var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset))
        }

        [data-framer-component-type=Text] a:hover,
        [data-framer-component-type=Text] a div span:hover,
        [data-framer-component-type=Text] a span span span:hover,
        [data-framer-component-type=Text] a p span span:hover,
        [data-framer-component-type=Text] a h1 span span:hover,
        [data-framer-component-type=Text] a h2 span span:hover,
        [data-framer-component-type=Text] a h3 span span:hover,
        [data-framer-component-type=Text] a h4 span span:hover,
        [data-framer-component-type=Text] a h5 span span:hover,
        [data-framer-component-type=Text] a h6 span span:hover {
            --font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family)));
            --font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style)));
            --font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));
            --text-color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color)));
            --font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size)));
            --text-transform: var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform)));
            --text-decoration: var(--framer-link-hover-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, solid)))var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))var(--framer-link-hover-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, currentcolor)))var(--framer-link-hover-text-decoration-thickness, var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, auto)));
            --text-decoration-skip-ink: var(--framer-link-hover-text-decoration-skip-ink, var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink)));
            --text-underline-offset: var(--framer-link-hover-text-decoration-offset, var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset)))
        }

        [data-framer-component-type=Text].isCurrent a,
        [data-framer-component-type=Text].isCurrent a div span,
        [data-framer-component-type=Text].isCurrent a span span span,
        [data-framer-component-type=Text].isCurrent a p span span,
        [data-framer-component-type=Text].isCurrent a h1 span span,
        [data-framer-component-type=Text].isCurrent a h2 span span,
        [data-framer-component-type=Text].isCurrent a h3 span span,
        [data-framer-component-type=Text].isCurrent a h4 span span,
        [data-framer-component-type=Text].isCurrent a h5 span span,
        [data-framer-component-type=Text].isCurrent a h6 span span {
            --font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family)));
            --font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style)));
            --font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight)));
            --text-color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color)));
            --font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size)));
            --text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform)));
            --text-decoration: var(--framer-link-current-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, solid)))var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))var(--framer-link-current-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, currentcolor)))var(--framer-link-current-text-decoration-thickness, var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, auto)));
            --text-decoration-skip-ink: var(--framer-link-current-text-decoration-skip-ink, var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink)));
            --text-underline-offset: var(--framer-link-current-text-decoration-offset, var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset)))
        }

        [data-framer-component-type=RichTextContainer] {
            outline: none;
            flex-direction: column;
            flex-shrink: 0;
            justify-content: flex-start;
            display: flex
        }

        p.framer-text,
        div.framer-text,
        figure.framer-text,
        h1.framer-text,
        h2.framer-text,
        h3.framer-text,
        h4.framer-text,
        h5.framer-text,
        h6.framer-text,
        ol.framer-text,
        ul.framer-text {
            margin: 0;
            padding: 0
        }

        p.framer-text,
        div.framer-text,
        h1.framer-text,
        h2.framer-text,
        h3.framer-text,
        h4.framer-text,
        h5.framer-text,
        h6.framer-text,
        li.framer-text,
        ol.framer-text,
        ul.framer-text,
        mark.framer-text,
        span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-blockquote-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-blockquote-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-blockquote-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-blockquote-text-color, var(--framer-text-color, #000));
            font-size: calc(var(--framer-blockquote-font-size, var(--framer-font-size, 16px))*var(--framer-font-size-scale, 1));
            letter-spacing: var(--framer-blockquote-letter-spacing, var(--framer-letter-spacing, 0));
            text-transform: var(--framer-blockquote-text-transform, var(--framer-text-transform, none));
            -webkit-text-decoration-line: var(--framer-blockquote-text-decoration, var(--framer-text-decoration, initial));
            text-decoration-line: var(--framer-blockquote-text-decoration, var(--framer-text-decoration, initial));
            -webkit-text-decoration-style: var(--framer-blockquote-text-decoration-style, var(--framer-text-decoration-style, initial));
            text-decoration-style: var(--framer-blockquote-text-decoration-style, var(--framer-text-decoration-style, initial));
            -webkit-text-decoration-color: var(--framer-blockquote-text-decoration-color, var(--framer-text-decoration-color, initial));
            text-decoration-color: var(--framer-blockquote-text-decoration-color, var(--framer-text-decoration-color, initial));
            text-decoration-thickness: var(--framer-blockquote-text-decoration-thickness, var(--framer-text-decoration-thickness, initial));
            text-decoration-skip-ink: var(--framer-blockquote-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink, initial));
            text-underline-offset: var(--framer-blockquote-text-decoration-offset, var(--framer-text-decoration-offset, initial));
            line-height: var(--framer-blockquote-line-height, var(--framer-line-height, 1.2em));
            text-align: var(--framer-blockquote-text-alignment, var(--framer-text-alignment, start));
            -webkit-text-stroke-width: var(--framer-text-stroke-width, initial);
            -webkit-text-stroke-color: var(--framer-text-stroke-color, initial);
            -moz-font-feature-settings: var(--framer-font-open-type-features, initial);
            -webkit-font-feature-settings: var(--framer-font-open-type-features, initial);
            font-feature-settings: var(--framer-font-open-type-features, initial);
            font-variation-settings: var(--framer-font-variation-axes, normal);
            text-wrap: var(--framer-text-wrap-override, var(--framer-text-wrap))
        }

        mark.framer-text,
        p.framer-text,
        div.framer-text,
        h1.framer-text,
        h2.framer-text,
        h3.framer-text,
        h4.framer-text,
        h5.framer-text,
        h6.framer-text,
        li.framer-text,
        ol.framer-text,
        ul.framer-text {
            background-color: var(--framer-blockquote-text-background-color, var(--framer-text-background-color, initial));
            border-radius: var(--framer-blockquote-text-background-radius, var(--framer-text-background-radius, initial));
            padding: var(--framer-blockquote-text-background-padding, var(--framer-text-background-padding, initial))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            p.framer-text,
            div.framer-text,
            h1.framer-text,
            h2.framer-text,
            h3.framer-text,
            h4.framer-text,
            h5.framer-text,
            h6.framer-text,
            li.framer-text,
            ol.framer-text,
            ul.framer-text,
            span.framer-text:not([data-text-fill]) {
                color: var(--framer-blockquote-text-color-rgb, var(--framer-blockquote-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))));
                -webkit-text-stroke-color: var(--framer-text-stroke-color-rgb, var(--framer-text-stroke-color, initial))
            }

            mark.framer-text {
                background-color: var(--framer-blockquote-text-background-color-rgb, var(--framer-blockquote-text-background-color, var(--framer-text-background-color-rgb, var(--framer-text-background-color, initial))))
            }
        }

        .framer-fit-text .framer-text {
            white-space: nowrap;
            white-space-collapse: preserve
        }

        strong.framer-text {
            font-family: var(--framer-blockquote-font-family-bold, var(--framer-font-family-bold));
            font-style: var(--framer-blockquote-font-style-bold, var(--framer-font-style-bold));
            font-weight: var(--framer-blockquote-font-weight-bold, var(--framer-font-weight-bold, bolder));
            font-variation-settings: var(--framer-blockquote-font-variation-axes-bold, var(--framer-font-variation-axes-bold))
        }

        em.framer-text {
            font-family: var(--framer-blockquote-font-family-italic, var(--framer-font-family-italic));
            font-style: var(--framer-blockquote-font-style-italic, var(--framer-font-style-italic, italic));
            font-weight: var(--framer-blockquote-font-weight-italic, var(--framer-font-weight-italic));
            font-variation-settings: var(--framer-blockquote-font-variation-axes-italic, var(--framer-font-variation-axes-italic))
        }

        em.framer-text>strong.framer-text {
            font-family: var(--framer-blockquote-font-family-bold-italic, var(--framer-font-family-bold-italic));
            font-style: var(--framer-blockquote-font-style-bold-italic, var(--framer-font-style-bold-italic, italic));
            font-weight: var(--framer-blockquote-font-weight-bold-italic, var(--framer-font-weight-bold-italic, bolder));
            font-variation-settings: var(--framer-blockquote-font-variation-axes-bold-italic, var(--framer-font-variation-axes-bold-italic))
        }

        p.framer-text:not(:first-child),
        div.framer-text:not(:first-child),
        h1.framer-text:not(:first-child),
        h2.framer-text:not(:first-child),
        h3.framer-text:not(:first-child),
        h4.framer-text:not(:first-child),
        h5.framer-text:not(:first-child),
        h6.framer-text:not(:first-child),
        ol.framer-text:not(:first-child),
        ul.framer-text:not(:first-child),
        blockquote.framer-text:not(:first-child),
        table.framer-text:not(:first-child),
        figure.framer-text:not(:first-child),
        .framer-image.framer-text:not(:first-child) {
            margin-top: var(--framer-blockquote-paragraph-spacing, var(--framer-paragraph-spacing, 0))
        }

        li.framer-text>ul.framer-text:nth-child(2),
        li.framer-text>ol.framer-text:nth-child(2) {
            margin-top: 0
        }

        .framer-text[data-text-fill] {
            -webkit-text-fill-color: transparent;
            padding: max(0em, calc(calc(1.3em - var(--framer-blockquote-line-height, var(--framer-line-height, 1.3em)))/2));
            margin: min(0em, calc(calc(1.3em - var(--framer-blockquote-line-height, var(--framer-line-height, 1.3em)))/-2));
            -webkit-background-clip: text;
            background-clip: text;
            display: inline-block
        }

        code.framer-text,
        code.framer-text span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-blockquote-font-style, var(--framer-code-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-blockquote-font-weight, var(--framer-code-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-blockquote-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)));
            font-size: calc(var(--framer-blockquote-font-size, var(--framer-font-size, 16px))*var(--framer-font-size-scale, 1));
            letter-spacing: var(--framer-blockquote-letter-spacing, var(--framer-letter-spacing, 0));
            line-height: var(--framer-blockquote-line-height, var(--framer-line-height, 1.2em))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            code.framer-text,
            code.framer-text span.framer-text:not([data-text-fill]) {
                color: var(--framer-blockquote-text-color-rgb, var(--framer-blockquote-text-color, var(--framer-code-text-color-rgb, var(--framer-code-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))
            }
        }

        blockquote.framer-text {
            unicode-bidi: initial;
            margin-block-start: initial;
            margin-block-end: initial;
            margin-inline-start: initial;
            margin-inline-end: initial
        }

        a.framer-text,
        a.framer-text span.framer-text:not([data-text-fill]),
        span.framer-text[data-nested-link],
        span.framer-text[data-nested-link] span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-blockquote-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
            font-style: var(--framer-blockquote-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-blockquote-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-blockquote-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
            font-size: calc(var(--framer-blockquote-font-size, var(--framer-font-size, 16px))*var(--framer-font-size-scale, 1));
            text-transform: var(--framer-blockquote-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
            cursor: var(--framer-custom-cursors, pointer);
            background-color: var(--framer-link-text-background-color, initial);
            border-radius: var(--framer-link-text-background-radius, initial);
            padding: var(--framer-link-text-background-padding, initial)
        }

        a.framer-text,
        span.framer-text[data-nested-link] {
            -webkit-text-decoration-line: var(--framer-blockquote-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial)));
            text-decoration-line: var(--framer-blockquote-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial)));
            -webkit-text-decoration-style: var(--framer-blockquote-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial)));
            text-decoration-style: var(--framer-blockquote-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial)));
            -webkit-text-decoration-color: var(--framer-blockquote-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial)));
            text-decoration-color: var(--framer-blockquote-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial)));
            text-decoration-thickness: var(--framer-blockquote-text-decoration-thickness, var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, initial)));
            text-decoration-skip-ink: var(--framer-blockquote-text-decoration-skip-ink, var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink, initial)));
            text-underline-offset: var(--framer-blockquote-text-decoration-offset, var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset, initial)))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            a.framer-text,
            a.framer-text span.framer-text:not([data-text-fill]),
            span.framer-text[data-nested-link],
            span.framer-text[data-nested-link] span.framer-text:not([data-text-fill]) {
                color: var(--framer-blockquote-text-color-rgb, var(--framer-blockquote-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))));
                background-color: var(--framer-link-text-background-color-rgb, var(--framer-link-text-background-color, initial))
            }
        }

        code.framer-text a.framer-text,
        code.framer-text a.framer-text span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-nested-link],
        code.framer-text span.framer-text[data-nested-link] span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-blockquote-font-style, var(--framer-code-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-blockquote-font-weight, var(--framer-code-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-blockquote-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-blockquote-font-size, var(--framer-font-size, 16px))*var(--framer-font-size-scale, 1))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            code.framer-text a.framer-text,
            code.framer-text a.framer-text span.framer-text:not([data-text-fill]),
            code.framer-text span.framer-text[data-nested-link],
            code.framer-text span.framer-text[data-nested-link] span.framer-text:not([data-text-fill]) {
                color: var(--framer-blockquote-text-color-rgb, var(--framer-blockquote-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-code-text-color-rgb, var(--framer-code-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))))
            }
        }

        a.framer-text:hover,
        a.framer-text:hover span.framer-text:not([data-text-fill]),
        span.framer-text[data-nested-link]:hover,
        span.framer-text[data-nested-link]:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-hover-font-family, var(--framer-blockquote-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));
            font-style: var(--framer-link-hover-font-style, var(--framer-blockquote-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));
            font-weight: var(--framer-link-hover-font-weight, var(--framer-blockquote-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));
            color: var(--framer-link-hover-text-color, var(--framer-blockquote-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-blockquote-font-size, var(--framer-font-size, 16px)))*var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-hover-text-transform, var(--framer-blockquote-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));
            background-color: var(--framer-link-hover-text-background-color, var(--framer-link-text-background-color, var(--framer-text-background-color, initial)));
            border-radius: var(--framer-link-hover-text-background-radius, var(--framer-link-text-background-radius, var(--framer-text-background-radius, initial)));
            padding: var(--framer-link-hover-text-background-padding, var(--framer-link-text-background-padding, var(--framer-text-background-padding, initial)))
        }

        a.framer-text:hover,
        span.framer-text[data-nested-link]:hover {
            -webkit-text-decoration-line: var(--framer-link-hover-text-decoration, var(--framer-blockquote-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial))));
            text-decoration-line: var(--framer-link-hover-text-decoration, var(--framer-blockquote-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial))));
            -webkit-text-decoration-style: var(--framer-link-hover-text-decoration-style, var(--framer-blockquote-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial))));
            text-decoration-style: var(--framer-link-hover-text-decoration-style, var(--framer-blockquote-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial))));
            -webkit-text-decoration-color: var(--framer-link-hover-text-decoration-color, var(--framer-blockquote-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial))));
            text-decoration-color: var(--framer-link-hover-text-decoration-color, var(--framer-blockquote-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial))));
            text-decoration-thickness: var(--framer-link-hover-text-decoration-thickness, var(--framer-blockquote-text-decoration-thickness, var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, initial))));
            text-decoration-skip-ink: var(--framer-link-hover-text-decoration-skip-ink, var(--framer-blockquote-text-decoration-skip-ink, var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink, initial))));
            text-underline-offset: var(--framer-link-hover-text-decoration-offset, var(--framer-blockquote-text-decoration-offset, var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset, initial))))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            a.framer-text:hover,
            a.framer-text:hover span.framer-text:not([data-text-fill]),
            span.framer-text[data-nested-link]:hover,
            span.framer-text[data-nested-link]:hover span.framer-text:not([data-text-fill]) {
                color: var(--framer-link-hover-text-color-rgb, var(--framer-link-hover-text-color, var(--framer-blockquote-text-color-rgb, var(--framer-blockquote-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))));
                background-color: var(--framer-link-hover-text-background-color-rgb, var(--framer-link-hover-text-background-color, var(--framer-link-text-background-color-rgb, var(--framer-link-text-background-color, var(--framer-text-background-color-rgb, var(--framer-text-background-color, initial))))))
            }
        }

        code.framer-text a.framer-text:hover,
        code.framer-text a.framer-text:hover span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-nested-link]:hover,
        code.framer-text span.framer-text[data-nested-link]:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-blockquote-font-style, var(--framer-code-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-blockquote-font-weight, var(--framer-code-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-link-hover-text-color, var(--framer-blockquote-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-blockquote-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))))*var(--framer-font-size-scale, 1))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            code.framer-text a.framer-text:hover,
            code.framer-text a.framer-text:hover span.framer-text:not([data-text-fill]),
            code.framer-text span.framer-text[data-nested-link]:hover,
            code.framer-text span.framer-text[data-nested-link]:hover span.framer-text:not([data-text-fill]) {
                color: var(--framer-link-hover-text-color-rgb, var(--framer-link-hover-text-color, var(--framer-blockquote-text-color-rgb, var(--framer-blockquote-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))))
            }
        }

        a.framer-text[data-framer-page-link-current],
        a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]),
        span.framer-text[data-framer-page-link-current],
        span.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
            font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
            font-size: calc(var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))*var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
            background-color: var(--framer-link-current-text-background-color, var(--framer-link-text-background-color, initial));
            border-radius: var(--framer-link-current-text-background-radius, var(--framer-link-text-background-radius, initial));
            padding: var(--framer-link-current-text-background-padding, var(--framer-link-text-background-padding, initial))
        }

        a.framer-text[data-framer-page-link-current],
        span.framer-text[data-framer-page-link-current] {
            -webkit-text-decoration-line: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial)));
            text-decoration-line: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial)));
            -webkit-text-decoration-style: var(--framer-link-current-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial)));
            text-decoration-style: var(--framer-link-current-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial)));
            -webkit-text-decoration-color: var(--framer-link-current-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial)));
            text-decoration-color: var(--framer-link-current-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial)));
            text-decoration-thickness: var(--framer-link-current-text-decoration-thickness, var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, initial)));
            text-decoration-skip-ink: var(--framer-link-current-text-decoration-skip-ink, var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink, initial)));
            text-underline-offset: var(--framer-link-current-text-decoration-offset, var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset, initial)))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            a.framer-text[data-framer-page-link-current],
            a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]),
            span.framer-text[data-framer-page-link-current],
            span.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
                color: var(--framer-link-current-text-color-rgb, var(--framer-link-current-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))));
                background-color: var(--framer-link-current-text-background-color-rgb, var(--framer-link-current-text-background-color, var(--framer-link-text-background-color-rgb, var(--framer-link-text-background-color, var(--framer-text-background-color-rgb, var(--framer-text-background-color, initial))))))
            }
        }

        code.framer-text a.framer-text[data-framer-page-link-current],
        code.framer-text a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-framer-page-link-current],
        code.framer-text span.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)))*var(--framer-font-size-scale, 1))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            code.framer-text a.framer-text[data-framer-page-link-current],
            code.framer-text a.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]),
            code.framer-text span.framer-text[data-framer-page-link-current],
            code.framer-text span.framer-text[data-framer-page-link-current] span.framer-text:not([data-text-fill]) {
                color: var(--framer-link-current-text-color-rgb, var(--framer-link-current-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-code-text-color-rgb, var(--framer-code-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))));
                background-color: var(--framer-link-current-text-background-color-rgb, var(--framer-link-current-text-background-color, var(--framer-link-text-background-color-rgb, var(--framer-link-text-background-color, var(--framer-text-background-color-rgb, var(--framer-text-background-color, initial))))))
            }
        }

        a.framer-text[data-framer-page-link-current]:hover,
        a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]),
        span.framer-text[data-framer-page-link-current]:hover,
        span.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-link-hover-font-family, var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));
            font-style: var(--framer-link-hover-font-style, var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));
            font-weight: var(--framer-link-hover-font-weight, var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));
            color: var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))))*var(--framer-font-size-scale, 1));
            text-transform: var(--framer-link-hover-text-transform, var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));
            background-color: var(--framer-link-hover-text-background-color, var(--framer-link-current-text-background-color, var(--framer-link-text-background-color, initial)));
            border-radius: var(--framer-link-hover-text-background-radius, var(--framer-link-current-text-background-radius, var(--framer-link-text-background-radius, initial)));
            padding: var(--framer-link-hover-text-background-padding, var(--framer-link-current-text-background-padding, var(--framer-link-text-background-padding, initial)))
        }

        a.framer-text[data-framer-page-link-current]:hover,
        span.framer-text[data-framer-page-link-current]:hover {
            -webkit-text-decoration-line: var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial))));
            text-decoration-line: var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, initial))));
            -webkit-text-decoration-style: var(--framer-link-hover-text-decoration-style, var(--framer-link-current-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial))));
            text-decoration-style: var(--framer-link-hover-text-decoration-style, var(--framer-link-current-text-decoration-style, var(--framer-link-text-decoration-style, var(--framer-text-decoration-style, initial))));
            -webkit-text-decoration-color: var(--framer-link-hover-text-decoration-color, var(--framer-link-current-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial))));
            text-decoration-color: var(--framer-link-hover-text-decoration-color, var(--framer-link-current-text-decoration-color, var(--framer-link-text-decoration-color, var(--framer-text-decoration-color, initial))));
            text-decoration-thickness: var(--framer-link-hover-text-decoration-thickness, var(--framer-link-current-text-decoration-thickness, var(--framer-link-text-decoration-thickness, var(--framer-text-decoration-thickness, initial))));
            text-decoration-skip-ink: var(--framer-link-hover-text-decoration-skip-ink, var(--framer-link-current-text-decoration-skip-ink, var(--framer-link-text-decoration-skip-ink, var(--framer-text-decoration-skip-ink, initial))));
            text-underline-offset: var(--framer-link-hover-text-decoration-offset, var(--framer-link-current-text-decoration-offset, var(--framer-link-text-decoration-offset, var(--framer-text-decoration-offset, initial))))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            a.framer-text[data-framer-page-link-current]:hover,
            a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]),
            span.framer-text[data-framer-page-link-current]:hover,
            span.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
                color: var(--framer-link-hover-text-color-rgb, var(--framer-link-hover-text-color, var(--framer-link-current-text-color-rgb, var(--framer-link-current-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-code-text-color-rgb, var(--framer-code-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))))));
                background-color: var(--framer-link-hover-text-background-color-rgb, var(--framer-link-hover-text-background-color, var(--framer-link-current-text-background-color-rgb, var(--framer-link-current-text-background-color, var(--framer-link-text-background-color-rgb, var(--framer-link-text-background-color, initial))))))
            }
        }

        code.framer-text a.framer-text[data-framer-page-link-current]:hover,
        code.framer-text a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-framer-page-link-current]:hover,
        code.framer-text span.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
            font-family: var(--framer-code-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-code-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-code-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-code-text-color, var(--framer-text-color, #000)))));
            font-size: calc(var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))))*var(--framer-font-size-scale, 1));
            background-color: var(--framer-link-hover-text-background-color, var(--framer-link-current-text-background-color, var(--framer-link-text-background-color, var(--framer-text-background-color, initial))));
            border-radius: var(--framer-link-hover-text-background-radius, var(--framer-link-current-text-background-radius, var(--framer-link-text-background-radius, var(--framer-text-background-radius, initial))));
            padding: var(--framer-link-hover-text-background-padding, var(--framer-link-current-text-background-padding, var(--framer-link-text-background-padding, var(--framer-text-background-padding, initial))))
        }

        @supports not (color:color(display-p3 1 1 1)) {

            code.framer-text a.framer-text[data-framer-page-link-current]:hover,
            code.framer-text a.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]),
            code.framer-text span.framer-text[data-framer-page-link-current]:hover,
            code.framer-text span.framer-text[data-framer-page-link-current]:hover span.framer-text:not([data-text-fill]) {
                color: var(--framer-link-hover-text-color-rgb, var(--framer-link-hover-text-color, var(--framer-link-current-text-color-rgb, var(--framer-link-current-text-color, var(--framer-link-text-color-rgb, var(--framer-link-text-color, var(--framer-code-text-color-rgb, var(--framer-code-text-color, var(--framer-text-color-rgb, var(--framer-text-color, #000))))))))));
                background-color: var(--framer-link-hover-text-background-color-rgb, var(--framer-link-hover-text-background-color, var(--framer-link-current-text-background-color-rgb, var(--framer-link-current-text-background-color, var(--framer-link-text-background-color-rgb, var(--framer-link-text-background-color, initial))))))
            }
        }

        .framer-image.framer-text {
            max-width: 100%;
            height: auto;
            display: block
        }

        .text-styles-preset-reset.framer-text {
            --framer-font-family: Inter, Inter Placeholder, sans-serif;
            --framer-font-style: normal;
            --framer-font-weight: 500;
            --framer-text-color: #000;
            --framer-font-size: 16px;
            --framer-letter-spacing: 0;
            --framer-text-transform: none;
            --framer-text-decoration: none;
            --framer-text-decoration-style: none;
            --framer-text-decoration-color: none;
            --framer-text-decoration-thickness: none;
            --framer-text-decoration-skip-ink: none;
            --framer-text-decoration-offset: none;
            --framer-line-height: 1.2em;
            --framer-text-alignment: start;
            --framer-font-open-type-features: normal;
            --framer-text-background-color: initial;
            --framer-text-background-radius: initial;
            --framer-text-background-padding: initial
        }

        ol.framer-text {
            --list-style-type: decimal
        }

        ul.framer-text,
        ol.framer-text {
            padding-left: 3ch;
            position: relative
        }

        li.framer-text {
            counter-increment: list-item;
            list-style: none
        }

        ol.framer-text>li.framer-text:before {
            content: counter(list-item, var(--list-style-type))".";
            font-variant-numeric: tabular-nums;
            position: absolute;
            left: 0
        }

        ol.framer-text>li.framer-text:nth-last-child(n+100),
        ol.framer-text>li.framer-text:nth-last-child(n+100)~li {
            padding-left: 1ch
        }

        ol.framer-text>li.framer-text:nth-last-child(n+1000),
        ol.framer-text>li.framer-text:nth-last-child(n+1000)~li {
            padding-left: 2ch
        }

        ol.framer-text>li.framer-text:nth-last-child(n+10000),
        ol.framer-text>li.framer-text:nth-last-child(n+10000)~li {
            padding-left: 3ch
        }

        ol.framer-text>li.framer-text:nth-last-child(n+100000),
        ol.framer-text>li.framer-text:nth-last-child(n+100000)~li {
            padding-left: 4ch
        }

        ol.framer-text>li.framer-text:nth-last-child(n+1000000),
        ol.framer-text>li.framer-text:nth-last-child(n+1000000)~li {
            padding-left: 5ch
        }

        ul.framer-text>li.framer-text:before {
            content: "•";
            position: absolute;
            left: 0
        }

        .framer-table-wrapper {
            overflow-x: auto
        }

        table.framer-text,
        .framer-table-wrapper table.framer-text {
            border-collapse: separate;
            border-spacing: 0;
            table-layout: auto;
            word-break: normal;
            width: 100%
        }

        td.framer-text,
        th.framer-text {
            vertical-align: top;
            min-width: 16ch
        }

        .framer-text-module[style*=aspect-ratio]>:first-child {
            width: 100%
        }

        @supports not (aspect-ratio:1) {
            .framer-text-module[style*=aspect-ratio] {
                position: relative
            }

            .framer-text-module[style*=aspect-ratio]:before {
                content: "";
                padding-bottom: calc(100%/calc(var(--aspect-ratio)));
                display: block
            }

            .framer-text-module[style*=aspect-ratio]>:first-child {
                height: 100%;
                position: absolute;
                top: 0;
                left: 0
            }
        }

        [data-framer-component-type=DeprecatedRichText] {
            cursor: inherit
        }

        [data-framer-component-type=DeprecatedRichText] .text-styles-preset-reset {
            --framer-font-family: Inter, Inter Placeholder, sans-serif;
            --framer-font-style: normal;
            --framer-font-weight: 500;
            --framer-text-color: #000;
            --framer-font-size: 16px;
            --framer-letter-spacing: 0;
            --framer-text-transform: none;
            --framer-text-decoration: none;
            --framer-line-height: 1.2em;
            --framer-text-alignment: start;
            --framer-font-open-type-features: normal;
            --font-variation-settings: normal
        }

        [data-framer-component-type=DeprecatedRichText] p,
        [data-framer-component-type=DeprecatedRichText] div,
        [data-framer-component-type=DeprecatedRichText] h1,
        [data-framer-component-type=DeprecatedRichText] h2,
        [data-framer-component-type=DeprecatedRichText] h3,
        [data-framer-component-type=DeprecatedRichText] h4,
        [data-framer-component-type=DeprecatedRichText] h5,
        [data-framer-component-type=DeprecatedRichText] h6 {
            margin: 0;
            padding: 0
        }

        [data-framer-component-type=DeprecatedRichText] p,
        [data-framer-component-type=DeprecatedRichText] div,
        [data-framer-component-type=DeprecatedRichText] h1,
        [data-framer-component-type=DeprecatedRichText] h2,
        [data-framer-component-type=DeprecatedRichText] h3,
        [data-framer-component-type=DeprecatedRichText] h4,
        [data-framer-component-type=DeprecatedRichText] h5,
        [data-framer-component-type=DeprecatedRichText] h6,
        [data-framer-component-type=DeprecatedRichText] li,
        [data-framer-component-type=DeprecatedRichText] ol,
        [data-framer-component-type=DeprecatedRichText] ul,
        [data-framer-component-type=DeprecatedRichText] span:not([data-text-fill]) {
            font-family: var(--framer-font-family, Inter, Inter Placeholder, sans-serif);
            font-style: var(--framer-font-style, normal);
            font-weight: var(--framer-font-weight, 400);
            color: var(--framer-text-color, #000);
            font-size: var(--framer-font-size, 16px);
            letter-spacing: var(--framer-letter-spacing, 0);
            text-transform: var(--framer-text-transform, none);
            -webkit-text-decoration: var(--framer-text-decoration, none);
            text-decoration: var(--framer-text-decoration, none);
            line-height: var(--framer-line-height, 1.2em);
            text-align: var(--framer-text-alignment, start)
        }

        [data-framer-component-type=DeprecatedRichText] p:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] div:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] h1:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] h2:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] h3:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] h4:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] h5:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] h6:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] ol:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] ul:not(:first-child),
        [data-framer-component-type=DeprecatedRichText] .framer-image:not(:first-child) {
            margin-top: var(--framer-paragraph-spacing, 0)
        }

        [data-framer-component-type=DeprecatedRichText] span[data-text-fill] {
            -webkit-text-fill-color: transparent;
            -webkit-background-clip: text;
            background-clip: text;
            display: inline-block
        }

        [data-framer-component-type=DeprecatedRichText] a,
        [data-framer-component-type=DeprecatedRichText] a span:not([data-text-fill]) {
            font-family: var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif));
            font-style: var(--framer-link-font-style, var(--framer-font-style, normal));
            font-weight: var(--framer-link-font-weight, var(--framer-font-weight, 400));
            color: var(--framer-link-text-color, var(--framer-text-color, #000));
            font-size: var(--framer-link-font-size, var(--framer-font-size, 16px));
            text-transform: var(--framer-link-text-transform, var(--framer-text-transform, none));
            -webkit-text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration, none));
            text-decoration: var(--framer-link-text-decoration, var(--framer-text-decoration, none))
        }

        [data-framer-component-type=DeprecatedRichText] a:hover,
        [data-framer-component-type=DeprecatedRichText] a:hover span:not([data-text-fill]) {
            font-family: var(--framer-link-hover-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
            font-style: var(--framer-link-hover-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-link-hover-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-link-hover-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
            font-size: var(--framer-link-hover-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));
            text-transform: var(--framer-link-hover-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
            -webkit-text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)));
            text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))
        }

        [data-framer-component-type=DeprecatedRichText] a[data-framer-page-link-current],
        [data-framer-component-type=DeprecatedRichText] a[data-framer-page-link-current] span:not([data-text-fill]):not([data-nested-link]) {
            font-family: var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif)));
            font-style: var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal)));
            font-weight: var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400)));
            color: var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000)));
            font-size: var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px)));
            text-transform: var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none)));
            -webkit-text-decoration: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)));
            text-decoration: var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none)))
        }

        [data-framer-component-type=DeprecatedRichText] a[data-framer-page-link-current]:hover,
        [data-framer-component-type=DeprecatedRichText] a[data-framer-page-link-current]:hover span:not([data-text-fill]):not([data-nested-link]) {
            font-family: var(--framer-link-hover-font-family, var(--framer-link-current-font-family, var(--framer-link-font-family, var(--framer-font-family, Inter, Inter Placeholder, sans-serif))));
            font-style: var(--framer-link-hover-font-style, var(--framer-link-current-font-style, var(--framer-link-font-style, var(--framer-font-style, normal))));
            font-weight: var(--framer-link-hover-font-weight, var(--framer-link-current-font-weight, var(--framer-link-font-weight, var(--framer-font-weight, 400))));
            color: var(--framer-link-hover-text-color, var(--framer-link-current-text-color, var(--framer-link-text-color, var(--framer-text-color, #000))));
            font-size: var(--framer-link-hover-font-size, var(--framer-link-current-font-size, var(--framer-link-font-size, var(--framer-font-size, 16px))));
            text-transform: var(--framer-link-hover-text-transform, var(--framer-link-current-text-transform, var(--framer-link-text-transform, var(--framer-text-transform, none))));
            -webkit-text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none))));
            text-decoration: var(--framer-link-hover-text-decoration, var(--framer-link-current-text-decoration, var(--framer-link-text-decoration, var(--framer-text-decoration, none))))
        }

        [data-framer-component-type=DeprecatedRichText] strong {
            font-weight: bolder
        }

        [data-framer-component-type=DeprecatedRichText] em {
            font-style: italic
        }

        [data-framer-component-type=DeprecatedRichText] .framer-image {
            max-width: 100%;
            height: auto;
            display: block
        }

        [data-framer-component-type=DeprecatedRichText] ul,
        [data-framer-component-type=DeprecatedRichText] ol {
            width: 100%;
            margin: 0;
            padding-left: 0;
            display: table
        }

        [data-framer-component-type=DeprecatedRichText] li {
            counter-increment: list-item;
            list-style: none;
            display: table-row
        }

        [data-framer-component-type=DeprecatedRichText] ol>li:before {
            box-sizing: border-box;
            content: counter(list-item)".";
            white-space: nowrap;
            width: 2.25ch;
            padding-right: .75ch;
            display: table-cell
        }

        [data-framer-component-type=DeprecatedRichText] ul>li:before {
            box-sizing: border-box;
            content: "•";
            width: 2.25ch;
            padding-right: .75ch;
            display: table-cell
        }

        :not([data-framer-generated])>[data-framer-stack-content-wrapper]>*,
        :not([data-framer-generated])>[data-framer-stack-content-wrapper]>[data-framer-component-type],
        :not([data-framer-generated])>[data-framer-stack-content-wrapper]>[data-framer-legacy-stack-gap-enabled]>*,
        :not([data-framer-generated])>[data-framer-stack-content-wrapper]>[data-framer-legacy-stack-gap-enabled]>[data-framer-component-type] {
            position: relative
        }

        .flexbox-gap-not-supported [data-framer-legacy-stack-gap-enabled=true]>*,
        [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false] {
            margin-top: calc(var(--stack-gap-y)/2);
            margin-bottom: calc(var(--stack-gap-y)/2);
            margin-right: calc(var(--stack-gap-x)/2);
            margin-left: calc(var(--stack-gap-x)/2)
        }

        [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled=true] {
            row-gap: var(--stack-native-row-gap);
            column-gap: var(--stack-native-column-gap)
        }

        .flexbox-gap-not-supported [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled=true] {
            row-gap: unset;
            column-gap: unset
        }

        .flexbox-gap-not-supported [data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true]>:first-child,
        [data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>:first-child,
        .flexbox-gap-not-supported [data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true]>:last-child,
        [data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>:last-child {
            margin-top: 0;
            margin-left: 0
        }

        .flexbox-gap-not-supported [data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true]>:last-child,
        [data-framer-stack-direction-reverse=false] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>:last-child,
        .flexbox-gap-not-supported [data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true]>:first-child,
        [data-framer-stack-direction-reverse=true] [data-framer-legacy-stack-gap-enabled=true][data-framer-stack-flexbox-gap=false]>:first-child {
            margin-bottom: 0;
            margin-right: 0
        }

        NavigationContainer [data-framer-component-type=NavigationContainer]>*,
        [data-framer-component-type=NavigationContainer]>[data-framer-component-type] {
            position: relative
        }

        [data-framer-component-type=Scroll]::-webkit-scrollbar {
            display: none
        }

        [data-framer-component-type=ScrollContentWrapper]>* {
            position: relative
        }

        [data-framer-component-type=NativeScroll] {
            -webkit-overflow-scrolling: touch
        }

        [data-framer-component-type=NativeScroll]>* {
            position: relative
        }

        [data-framer-component-type=NativeScroll].direction-both {
            overflow: auto
        }

        [data-framer-component-type=NativeScroll].direction-vertical {
            overflow: hidden auto
        }

        [data-framer-component-type=NativeScroll].direction-horizontal {
            overflow: auto hidden
        }

        [data-framer-component-type=NativeScroll].direction-vertical>* {
            width: 100% !important
        }

        [data-framer-component-type=NativeScroll].direction-horizontal>* {
            height: 100% !important
        }

        [data-framer-component-type=NativeScroll].scrollbar-hidden::-webkit-scrollbar {
            display: none
        }

        [data-framer-component-type=PageContentWrapper]>*,
        [data-framer-component-type=PageContentWrapper]>[data-framer-component-type] {
            position: relative
        }

        [data-framer-component-type=DeviceComponent].no-device>* {
            width: 100% !important;
            height: 100% !important
        }

        [data-is-present=false],
        [data-is-present=false] * {
            pointer-events: none !important
        }

        [data-framer-cursor=pointer] {
            cursor: pointer
        }

        [data-framer-cursor=grab] {
            cursor: grab
        }

        [data-framer-cursor=grab]:active {
            cursor: grabbing
        }

        [data-framer-component-type=Frame] *,
        [data-framer-component-type=Stack] * {
            pointer-events: auto
        }

        [data-framer-generated] * {
            pointer-events: unset
        }

        .svgContainer svg {
            display: block
        }

        [data-reset=button] {
            background: 0 0;
            border-width: 0;
            padding: 0
        }

        [data-hide-scrollbars=true]::-webkit-scrollbar {
            width: 0;
            height: 0
        }

        [data-hide-scrollbars=true]::-webkit-scrollbar-thumb {
            background: 0 0
        }

        [data-hide-scrollbars=true] {
            scrollbar-width: none
        }

        @supports not (overflow:clip) {
            :root {
                --overflow-clip-fallback: hidden
            }
        }

        .framer-lightbox-container {
            opacity: 1 !important;
            pointer-events: auto !important
        }

        @supports (background:-webkit-named-image(i)) and (not (contain-intrinsic-size:inherit)) {

            div.framer-text,
            p.framer-text,
            h1.framer-text,
            h2.framer-text,
            h3.framer-text,
            h4.framer-text,
            h5.framer-text,
            h6.framer-text,
            ol.framer-text,
            ul.framer-text,
            li.framer-text,
            blockquote.framer-text,
            .framer-text.framer-image {
                display: var(--text-truncation-display-inline-for-safari-16, revert)
            }

            div.framer-text:after,
            p.framer-text:after,
            h1.framer-text:after,
            h2.framer-text:after,
            h3.framer-text:after,
            h4.framer-text:after,
            h5.framer-text:after,
            h6.framer-text:after,
            ol.framer-text:after,
            ul.framer-text:after,
            li.framer-text:after,
            blockquote.framer-text:after,
            .framer-text.framer-image:after {
                content: var(--text-truncation-line-break-for-safari-16);
                white-space: pre
            }

            .framer-text.framer-text-module,
            .framer-text.framer-table-wrapper {
                display: var(--text-truncation-display-none-for-safari-16, revert)
            }

            p.framer-text[data-text-fill] {
                display: var(--text-truncation-display-inline-for-safari-16, inline-block)
            }
        }

        .framer-cursor-none,
        .framer-cursor-none * {
            cursor: none !important
        }

        .framer-pointer-events-none,
        .framer-pointer-events-none * {
            pointer-events: none !important
        }

        @supports (aspect-ratio:1) {
            body {
                --framer-aspect-ratio-supported: auto
            }
        }

        .framer-zh2Qm.framer-lux5qc,
        .framer-zh2Qm .framer-lux5qc {
            display: block
        }

        .framer-zh2Qm.framer-72rtr7 {
            height: 5436px;
            overflow: var(--overflow-clip-fallback, clip);
            background-color: #fff;
            width: 1519px;
            position: relative
        }

        .framer-zh2Qm .framer-1ut41i,
        .framer-zh2Qm .framer-emsfbm {
            background-color: #fff;
            flex: none;
            gap: 0;
            width: 1517px;
            height: 5437px;
            position: absolute;
            top: 0;
            left: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1nmh50q,
        .framer-zh2Qm .framer-1pcllk4 {
            flex: none;
            gap: 0;
            height: 5437px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-dabyx6,
        .framer-zh2Qm .framer-k9n2df {
            background-color: #fafafa;
            flex: none;
            gap: 0;
            height: 851px;
            position: absolute;
            top: 1418px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1dpisdd,
        .framer-zh2Qm .framer-1jmihuq {
            background-color: #fff;
            flex: none;
            gap: 0;
            height: 449px;
            position: absolute;
            top: 2269px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-11nbivf,
        .framer-zh2Qm .framer-1ubyxy8 {
            background-color: #fafafa;
            flex: none;
            gap: 0;
            height: 618px;
            position: absolute;
            top: 2718px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-12pexn1,
        .framer-zh2Qm .framer-19blid6 {
            background-color: #fff;
            flex: none;
            gap: 0;
            height: 551px;
            position: absolute;
            top: 3336px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1tgqfob,
        .framer-zh2Qm .framer-1b15hyq {
            background-color: #fafafa;
            flex: none;
            gap: 0;
            height: 368px;
            position: absolute;
            top: 3887px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-15xdfl1,
        .framer-zh2Qm .framer-1smk3m0 {
            background-color: #fff;
            flex: none;
            gap: 0;
            height: 342px;
            position: absolute;
            top: 4255px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1f1yors,
        .framer-zh2Qm .framer-sjfhmj {
            background-color: #fafafa;
            flex: none;
            gap: 0;
            height: 535px;
            position: absolute;
            top: 4597px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1juyrop,
        .framer-zh2Qm .framer-rahw6f {
            background-color: #1a1a1a;
            flex: none;
            gap: 0;
            height: 305px;
            position: absolute;
            top: 5132px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1289oy1,
        .framer-zh2Qm .framer-1xi6fhc {
            flex: none;
            gap: 0;
            height: 209px;
            position: absolute;
            top: 48px;
            left: 59px;
            right: 59px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1gfzmwc,
        .framer-zh2Qm .framer-1yzrq8 {
            flex: none;
            gap: 0;
            height: 130px;
            position: absolute;
            top: 0;
            left: 32px;
            right: 32px;
            overflow: visible
        }

        .framer-zh2Qm .framer-6dh5zk,
        .framer-zh2Qm .framer-z2ogxn {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 416px;
            height: auto;
            position: absolute;
            top: 55px;
            left: 0
        }

        .framer-zh2Qm .framer-1s4kr0v,
        .framer-zh2Qm .framer-9lrf60 {
            flex: none;
            gap: 0;
            height: 130px;
            position: absolute;
            top: 0;
            left: 668px;
            right: 358px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1xnp0eo,
        .framer-zh2Qm .framer-jaeku3 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 64px;
            height: auto;
            position: absolute;
            top: -1px;
            left: 0
        }

        .framer-zh2Qm .framer-eiy48r,
        .framer-zh2Qm .framer-zpdqlq {
            flex: none;
            gap: 0;
            height: 92px;
            position: absolute;
            top: 38px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1ob80d1,
        .framer-zh2Qm .framer-soe31f {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 68px;
            height: auto;
            position: absolute;
            top: 0;
            left: 0
        }

        .framer-zh2Qm .framer-156jeac,
        .framer-zh2Qm .framer-10emcuz {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 138px;
            height: auto;
            position: absolute;
            top: 34px;
            left: 0
        }

        .framer-zh2Qm .framer-ky553h,
        .framer-zh2Qm .framer-1dqvloq {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 58px;
            height: auto;
            position: absolute;
            top: 69px;
            left: 0
        }

        .framer-zh2Qm .framer-13ic58u,
        .framer-zh2Qm .framer-1017hqs {
            flex: none;
            gap: 0;
            height: 130px;
            position: absolute;
            top: 0;
            left: 1026px;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-lc7zym,
        .framer-zh2Qm .framer-i0soma {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 120px;
            height: auto;
            position: absolute;
            top: -1px;
            left: 0
        }

        .framer-zh2Qm .framer-zux7mr,
        .framer-zh2Qm .framer-1qprglt {
            flex: none;
            gap: 0;
            height: 36px;
            position: absolute;
            top: 38px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-17f5q3k,
        .framer-zh2Qm .framer-1gllqq9 {
            background-color: #333;
            border-radius: 33554400px;
            flex: none;
            gap: 0;
            height: 36px;
            position: absolute;
            top: 0;
            left: 0;
            right: 274px;
            overflow: visible
        }

        .framer-zh2Qm .framer-63ogap,
        .framer-zh2Qm .framer-q5zhuq {
            background-color: #333;
            border-radius: 33554400px;
            flex: none;
            gap: 0;
            height: 36px;
            position: absolute;
            top: 0;
            left: 48px;
            right: 226px;
            overflow: visible
        }

        .framer-zh2Qm .framer-keri7o,
        .framer-zh2Qm .framer-rlzjdg {
            background-color: #333;
            border-radius: 33554400px;
            flex: none;
            gap: 0;
            height: 36px;
            position: absolute;
            top: 0;
            left: 96px;
            right: 178px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1nqshh2,
        .framer-zh2Qm .framer-1rehvw3 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 10px;
            height: auto;
            position: absolute;
            top: 8px;
            left: 13px
        }

        .framer-zh2Qm .framer-4a0xm0,
        .framer-zh2Qm .framer-14d7byc {
            --border-bottom-width: 0px;
            --border-color: #333;
            --border-left-width: 0px;
            --border-right-width: 0px;
            --border-style: solid;
            --border-top-width: 1px;
            flex: none;
            gap: 0;
            height: 47px;
            position: absolute;
            top: 162px;
            left: 32px;
            right: 32px;
            overflow: visible
        }

        .framer-zh2Qm .framer-enn12q,
        .framer-zh2Qm .framer-1j12pm8 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 24px;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-z28w2h,
        .framer-zh2Qm .framer-1bocf61 {
            flex: none;
            width: 17px;
            height: 17px;
            position: absolute;
            top: 5227px;
            left: calc(74.7858% - 8.5px)
        }

        .framer-zh2Qm .framer-qmdq6f,
        .framer-zh2Qm .framer-1gtwy07 {
            flex: none;
            width: 17px;
            height: 17px;
            position: absolute;
            top: 5227px;
            left: calc(77.9499% - 8.5px)
        }

        .framer-zh2Qm .framer-bgxf5h,
        .framer-zh2Qm .framer-lkf6ti {
            background: linear-gradient(#fff 0%, #f8f9fb 40%, #e8eef5 100%);
            flex: none;
            gap: 0;
            height: 1337px;
            position: absolute;
            top: 81px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-urb4p0,
        .framer-zh2Qm .framer-sajmdb {
            -webkit-backdrop-filter: blur(2px);
            backdrop-filter: blur(2px);
            background: linear-gradient(#fafafa 53.987%, #8ba9bcb3 88.9743%, #357fae8c 94.7798%);
            flex: none;
            width: 1517px;
            height: 329px;
            position: absolute;
            top: 404px;
            left: 0
        }

        .framer-zh2Qm .framer-1g8kjve {
            filter: blur(2px);
            flex: none;
            height: 629px;
            position: absolute;
            top: 708px;
            left: 0;
            right: 0
        }

        .framer-zh2Qm .framer-f1rnyd,
        .framer-zh2Qm .framer-1nbu5th {
            flex: none;
            gap: 0;
            height: 545px;
            position: absolute;
            top: 128px;
            left: 309px;
            right: 309px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1cw9x66,
        .framer-zh2Qm .framer-1oo83ln {
            flex: none;
            gap: 0;
            height: 352px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-3mt1k2,
        .framer-zh2Qm .framer-16rp71d {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            width: 753px;
            height: auto;
            position: absolute;
            top: -4px;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-fqr5u8,
        .framer-zh2Qm .framer-9uwdwr {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            width: 745px;
            height: auto;
            position: absolute;
            top: 172px;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-n32a2t,
        .framer-zh2Qm .framer-tp7361 {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            width: 700px;
            height: auto;
            position: absolute;
            top: 388px;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-bj2mjp,
        .framer-zh2Qm .framer-1ybfcli {
            flex: none;
            gap: 0;
            height: 56px;
            position: absolute;
            top: 489px;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1ppn2qm,
        .framer-zh2Qm .framer-17ab0ds {
            will-change: var(--framer-will-change-override, transform);
            background-color: #0244ae;
            border-radius: 10px;
            flex: none;
            gap: 0;
            width: 169px;
            height: 56px;
            position: absolute;
            top: calc(50% - 28px);
            left: calc(37.5191% - 84.2813px);
            overflow: hidden;
            box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000, 0 0 #0000, 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a
        }

        .framer-zh2Qm .framer-exwksb,
        .framer-zh2Qm .framer-uvsyr5 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 49%;
            left: 50%;
            transform: translate(-50%, -50%)
        }

        .framer-zh2Qm .framer-k2iu7j,
        .framer-zh2Qm .framer-sktn4y {
            --border-bottom-width: 1px;
            --border-color: #ff2b29;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #ffffff03;
            border-radius: 10px;
            flex: none;
            gap: 0;
            width: 209px;
            height: 56px;
            position: absolute;
            top: calc(50% - 28px);
            left: calc(60.2526% - 104.32px);
            overflow: visible;
            box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000, 0 0 #0000, 0 1px 2px #0000000d
        }

        .framer-zh2Qm .framer-1vl1z9x,
        .framer-zh2Qm .framer-1c5yww6 {
            background-color: #ff2b29;
            border-radius: 33554400px;
            flex: none;
            gap: 0;
            width: 32px;
            height: 32px;
            position: absolute;
            top: calc(50% - 16px);
            left: calc(23.9647% - 16px);
            overflow: visible
        }

        .framer-zh2Qm .framer-1am15z1,
        .framer-zh2Qm .framer-1bz3k2n {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 49%;
            left: 61%;
            transform: translate(-50%, -50%)
        }

        .framer-zh2Qm .framer-kn0a0i,
        .framer-zh2Qm .framer-1rhvh6x {
            flex: none;
            width: 17px;
            height: 16px;
            position: absolute;
            top: 509px;
            left: calc(54.3281% - 8.5px)
        }

        .framer-zh2Qm .framer-yex2ii,
        .framer-zh2Qm .framer-61nycl {
            flex: none;
            gap: 0;
            height: 691px;
            position: absolute;
            top: 1498px;
            left: 59px;
            right: 59px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1dz40gf,
        .framer-zh2Qm .framer-1yoc00u {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 362px;
            height: auto;
            position: absolute;
            top: 0;
            left: 32px
        }

        .framer-zh2Qm .framer-19cvk3g,
        .framer-zh2Qm .framer-16ldven {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            will-change: var(--framer-will-change-override, transform);
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 583px;
            position: absolute;
            top: 108px;
            left: 32px;
            right: 716px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-vztvj5,
        .framer-zh2Qm .framer-ath030,
        .framer-zh2Qm .framer-pyyw4m,
        .framer-zh2Qm .framer-14gsmt4 {
            flex: none;
            gap: 0;
            height: 400px;
            position: absolute;
            top: 1px;
            left: 1px;
            right: 1px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-tk5tb,
        .framer-zh2Qm .framer-zkr7g3,
        .framer-zh2Qm .framer-8v2yyj,
        .framer-zh2Qm .framer-9q1tw1 {
            flex: none;
            gap: 0;
            height: 181px;
            position: absolute;
            top: 401px;
            left: 1px;
            right: 1px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1wgkbrl,
        .framer-zh2Qm .framer-z2741a {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 133px;
            height: auto;
            position: absolute;
            top: 28px;
            left: 28px
        }

        .framer-zh2Qm .framer-1fix5xc,
        .framer-zh2Qm .framer-1fc9t6i {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 193px;
            height: auto;
            position: absolute;
            top: 69px;
            left: 28px
        }

        .framer-zh2Qm .framer-ldragp,
        .framer-zh2Qm .framer-1wgzqjo,
        .framer-zh2Qm .framer-1cw171m,
        .framer-zh2Qm .framer-1md83vf {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 107px;
            left: 28px;
            right: 28px
        }

        .framer-zh2Qm .framer-1r740jn,
        .framer-zh2Qm .framer-1e6fgsh {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            will-change: var(--framer-will-change-override, transform);
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 583px;
            position: absolute;
            top: 108px;
            left: 716px;
            right: 32px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-cj9ekn,
        .framer-zh2Qm .framer-1188d38 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 147px;
            height: auto;
            position: absolute;
            top: 28px;
            left: 28px
        }

        .framer-zh2Qm .framer-4y3e26,
        .framer-zh2Qm .framer-13w21p4 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 160px;
            height: auto;
            position: absolute;
            top: 69px;
            left: 28px
        }

        .framer-zh2Qm .framer-wa8pnq,
        .framer-zh2Qm .framer-1c18fdf {
            flex: none;
            gap: 0;
            width: 1000px;
            height: 289px;
            position: absolute;
            top: 2349px;
            left: calc(50.033% - 500px);
            overflow: visible
        }

        .framer-zh2Qm .framer-14hwcki,
        .framer-zh2Qm .framer-ozsvr8,
        .framer-zh2Qm .framer-1po5h49,
        .framer-zh2Qm .framer-1kul0ly,
        .framer-zh2Qm .framer-4gjnmf,
        .framer-zh2Qm .framer-1pwm5vk {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-9wsm9z,
        .framer-zh2Qm .framer-m8gayt {
            flex: none;
            gap: 0;
            height: 181px;
            position: absolute;
            top: 108px;
            left: 32px;
            right: 32px;
            overflow: visible
        }

        .framer-zh2Qm .framer-15mzms5,
        .framer-zh2Qm .framer-6wlq37 {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 5px;
            left: 0;
            right: 492px
        }

        .framer-zh2Qm .framer-1hcvawa,
        .framer-zh2Qm .framer-u6k9il {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 5px;
            left: 492px;
            right: 0
        }

        .framer-zh2Qm .framer-cei7t6,
        .framer-zh2Qm .framer-ce7oq4 {
            flex: none;
            gap: 0;
            height: 458px;
            position: absolute;
            top: 2798px;
            left: 59px;
            right: 59px;
            overflow: visible
        }

        .framer-zh2Qm .framer-ux665o,
        .framer-zh2Qm .framer-fxooaf {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 445px;
            height: auto;
            position: absolute;
            top: 0;
            left: 478px
        }

        .framer-zh2Qm .framer-1dbo1au,
        .framer-zh2Qm .framer-f19z {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            will-change: var(--framer-will-change-override, transform);
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 350px;
            position: absolute;
            top: 108px;
            left: 32px;
            right: 944px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-o5eqwq,
        .framer-zh2Qm .framer-2ielw0,
        .framer-zh2Qm .framer-n7dmha,
        .framer-zh2Qm .framer-1it48r1,
        .framer-zh2Qm .framer-1ctyu9q,
        .framer-zh2Qm .framer-11rjkpd {
            flex: none;
            gap: 0;
            height: 200px;
            position: absolute;
            top: 1px;
            left: 1px;
            right: 1px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-kuxeq4,
        .framer-zh2Qm .framer-k6mr6x,
        .framer-zh2Qm .framer-ilm27q,
        .framer-zh2Qm .framer-g2fb20,
        .framer-zh2Qm .framer-ntm4ju,
        .framer-zh2Qm .framer-db7yfz {
            flex: none;
            gap: 0;
            height: 148px;
            position: absolute;
            top: 201px;
            left: 1px;
            right: 1px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1q5vahi,
        .framer-zh2Qm .framer-1a77hvy {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 50px;
            height: auto;
            position: absolute;
            top: 28px;
            left: 186px
        }

        .framer-zh2Qm .framer-1n8yjv3,
        .framer-zh2Qm .framer-xrppr8 {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 74px;
            left: 57px;
            right: -1px
        }

        .framer-zh2Qm .framer-1kh81of,
        .framer-zh2Qm .framer-16rttp2 {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            will-change: var(--framer-will-change-override, transform);
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 350px;
            position: absolute;
            top: 108px;
            left: 488px;
            right: 488px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-1f53pjo,
        .framer-zh2Qm .framer-26cqmb {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 93px;
            height: auto;
            position: absolute;
            top: 28px;
            left: 163px
        }

        .framer-zh2Qm .framer-1f5fay3,
        .framer-zh2Qm .framer-otnao8 {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 74px;
            left: 35px;
            right: 35px
        }

        .framer-zh2Qm .framer-1ayrngw,
        .framer-zh2Qm .framer-1bzvda2 {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            will-change: var(--framer-will-change-override, transform);
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 350px;
            position: absolute;
            top: 108px;
            left: 944px;
            right: 32px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-mfo6dc,
        .framer-zh2Qm .framer-1hstmqn {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 118px;
            height: auto;
            position: absolute;
            top: 28px;
            left: 150px
        }

        .framer-zh2Qm .framer-pbg0om,
        .framer-zh2Qm .framer-16qv9ev {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 74px;
            left: 31px;
            right: 25px
        }

        .framer-zh2Qm .framer-14r6s4k,
        .framer-zh2Qm .framer-1vr9t0 {
            flex: none;
            gap: 0;
            height: 391px;
            position: absolute;
            top: 3416px;
            left: 59px;
            right: 59px;
            overflow: visible
        }

        .framer-zh2Qm .framer-7q5080,
        .framer-zh2Qm .framer-1bmqcxv {
            flex: none;
            gap: 0;
            height: 60px;
            position: absolute;
            top: 0;
            left: 32px;
            right: 32px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1iec8cg,
        .framer-zh2Qm .framer-mk030y {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 705px;
            height: auto;
            position: absolute;
            top: 0;
            left: 298px
        }

        .framer-zh2Qm .framer-q9f7e8,
        .framer-zh2Qm .framer-ozyjis {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 547px;
            height: auto;
            position: absolute;
            top: 75px;
            left: 410px
        }

        .framer-zh2Qm .framer-8uy8fb,
        .framer-zh2Qm .framer-jbfc65 {
            flex: none;
            gap: 0;
            height: 241px;
            position: absolute;
            top: 149px;
            left: 32px;
            right: 32px;
            overflow: visible
        }

        .framer-zh2Qm .framer-smsd1w,
        .framer-zh2Qm .framer-b5jtqg {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #fafafa;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 241px;
            position: absolute;
            top: 0;
            left: 0;
            right: 912px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1nnwkm6,
        .framer-zh2Qm .framer-111jwgy,
        .framer-zh2Qm .framer-juov0z,
        .framer-zh2Qm .framer-1rw71r9,
        .framer-zh2Qm .framer-1qhvu,
        .framer-zh2Qm .framer-1bsxeae {
            flex: none;
            gap: 0;
            height: 56px;
            position: absolute;
            top: 33px;
            left: 33px;
            right: 33px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1alm3o6,
        .framer-zh2Qm .framer-sfhyq4,
        .framer-zh2Qm .framer-ii1a9e,
        .framer-zh2Qm .framer-jceoua {
            background-color: #0244ae;
            border-radius: 33554400px;
            flex: none;
            gap: 0;
            height: 56px;
            position: absolute;
            top: calc(50% - 28px);
            left: 0;
            right: 302px;
            overflow: visible
        }

        .framer-zh2Qm .framer-n9j5pl,
        .framer-zh2Qm .framer-tr5wjj {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 13px;
            height: auto;
            position: absolute;
            top: 50%;
            left: 21px;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-16027pc,
        .framer-zh2Qm .framer-1r1qnsl {
            flex: none;
            gap: 0;
            height: 44px;
            position: absolute;
            top: calc(49.9861% - 22.1953px);
            left: 72px;
            right: 171px;
            overflow: visible
        }

        .framer-zh2Qm .framer-ksrljw,
        .framer-zh2Qm .framer-ko53z0 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 111px;
            height: auto;
            position: absolute;
            top: 0;
            left: 0
        }

        .framer-zh2Qm .framer-z4c4c6,
        .framer-zh2Qm .framer-p8zwjy {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 87px;
            height: auto;
            position: absolute;
            top: 24px;
            left: 0
        }

        .framer-zh2Qm .framer-9qb6vu,
        .framer-zh2Qm .framer-iwvze7,
        .framer-zh2Qm .framer-1o6ft3b,
        .framer-zh2Qm .framer-co3opv,
        .framer-zh2Qm .framer-heu1cu,
        .framer-zh2Qm .framer-aemnh2 {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            height: auto;
            position: absolute;
            top: 108px;
            left: 33px;
            right: 33px
        }

        .framer-zh2Qm .framer-10w9ehf,
        .framer-zh2Qm .framer-1blb1af {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #fafafa;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 241px;
            position: absolute;
            top: 0;
            left: 456px;
            right: 456px;
            overflow: visible
        }

        .framer-zh2Qm .framer-uo1fpq,
        .framer-zh2Qm .framer-wnv9ck {
            background-color: #ff2b29;
            border-radius: 33554400px;
            flex: none;
            gap: 0;
            height: 56px;
            position: absolute;
            top: calc(50% - 28px);
            left: 0;
            right: 302px;
            overflow: visible
        }

        .framer-zh2Qm .framer-je6gug,
        .framer-zh2Qm .framer-17qkmiy {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 15px;
            height: auto;
            position: absolute;
            top: 50%;
            left: 21px;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-rduinn,
        .framer-zh2Qm .framer-1cywl9b {
            flex: none;
            gap: 0;
            height: 44px;
            position: absolute;
            top: calc(49.9861% - 22.1953px);
            left: 72px;
            right: 150px;
            overflow: visible
        }

        .framer-zh2Qm .framer-7ymvx6,
        .framer-zh2Qm .framer-l9v8xu {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 132px;
            height: auto;
            position: absolute;
            top: 0;
            left: 0
        }

        .framer-zh2Qm .framer-1qtdh2w,
        .framer-zh2Qm .framer-dbomlg {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 92px;
            height: auto;
            position: absolute;
            top: 24px;
            left: 0
        }

        .framer-zh2Qm .framer-1qxrfg,
        .framer-zh2Qm .framer-9ta0y6 {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #fafafa;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 241px;
            position: absolute;
            top: 0;
            left: 912px;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-oygpat,
        .framer-zh2Qm .framer-r8ovew {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 11px;
            height: auto;
            position: absolute;
            top: 50%;
            left: 22px;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-11ulwfd,
        .framer-zh2Qm .framer-1yoh7by {
            flex: none;
            gap: 0;
            height: 44px;
            position: absolute;
            top: calc(49.9861% - 22.1953px);
            left: 72px;
            right: 161px;
            overflow: visible
        }

        .framer-zh2Qm .framer-wcx1m2,
        .framer-zh2Qm .framer-1l1k1is {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 114px;
            height: auto;
            position: absolute;
            top: 0;
            left: 0
        }

        .framer-zh2Qm .framer-kxnwcy,
        .framer-zh2Qm .framer-1os0mrq {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 121px;
            height: auto;
            position: absolute;
            top: 24px;
            left: 0
        }

        .framer-zh2Qm .framer-10yfe07,
        .framer-zh2Qm .framer-voizp4 {
            flex: none;
            gap: 0;
            height: 208px;
            position: absolute;
            top: 3967px;
            left: 59px;
            right: 59px;
            overflow: visible
        }

        .framer-zh2Qm .framer-ealtwa,
        .framer-zh2Qm .framer-s1ajnp {
            --border-bottom-width: 1px;
            --border-color: #0244ae;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #fff;
            border-radius: 8px;
            flex: none;
            gap: 0;
            width: 178px;
            height: 40px;
            position: absolute;
            top: 84px;
            left: calc(50.0268% - 88.875px);
            overflow: visible
        }

        .framer-zh2Qm .framer-1etngah,
        .framer-zh2Qm .framer-qxlxlw {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 8px;
            left: 51%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-jghevh,
        .framer-zh2Qm .framer-1ggorj7 {
            --framer-paragraph-spacing: 0px;
            white-space: pre-wrap;
            word-break: break-word;
            word-wrap: break-word;
            flex: none;
            width: 700px;
            height: auto;
            position: absolute;
            top: 153px;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-w9zt0g,
        .framer-zh2Qm .framer-oqprjs {
            flex: none;
            gap: 0;
            height: 182px;
            position: absolute;
            top: 4335px;
            left: 59px;
            right: 59px;
            overflow: visible
        }

        .framer-zh2Qm .framer-j8emao,
        .framer-zh2Qm .framer-3kne09 {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 856px;
            height: auto;
            position: absolute;
            top: 0;
            left: 252px
        }

        .framer-zh2Qm .framer-fkiakh,
        .framer-zh2Qm .framer-gtl63p {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 849px;
            height: auto;
            position: absolute;
            top: 75px;
            left: 256px
        }

        .framer-zh2Qm .framer-iejzl4,
        .framer-zh2Qm .framer-1v2n5ps {
            background-color: #0244ae;
            border-radius: 8px;
            flex: none;
            gap: 0;
            width: 169px;
            height: 48px;
            position: absolute;
            top: 134px;
            left: calc(49.9844% - 84.2813px);
            overflow: visible
        }

        .framer-zh2Qm .framer-1dwfmnm,
        .framer-zh2Qm .framer-1t3jlbl {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-1f923kb,
        .framer-zh2Qm .framer-1v8qv82 {
            flex: none;
            gap: 0;
            height: 375px;
            position: absolute;
            top: 4677px;
            left: 309px;
            right: 309px;
            overflow: visible
        }

        .framer-zh2Qm .framer-17d3mw6,
        .framer-zh2Qm .framer-1hubfum {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 399px;
            height: auto;
            position: absolute;
            top: 72px;
            left: 242px
        }

        .framer-zh2Qm .framer-1uwoi7l,
        .framer-zh2Qm .framer-16my1ex {
            flex: none;
            gap: 0;
            height: 231px;
            position: absolute;
            top: 144px;
            left: 32px;
            right: 32px;
            overflow: visible
        }

        .framer-zh2Qm .framer-1uvfe50,
        .framer-zh2Qm .framer-1pa78gp {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 231px;
            position: absolute;
            top: 0;
            left: 0;
            right: 434px;
            overflow: visible
        }

        .framer-zh2Qm .framer-13ifh5,
        .framer-zh2Qm .framer-1gc83r,
        .framer-zh2Qm .framer-2jlx2z,
        .framer-zh2Qm .framer-1vyacqz {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 32px;
            height: auto;
            position: absolute;
            top: 33px;
            left: 179px
        }

        .framer-zh2Qm .framer-1uq16m6,
        .framer-zh2Qm .framer-jb5ixq {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 61px;
            height: auto;
            position: absolute;
            top: 96px;
            left: 170px
        }

        .framer-zh2Qm .framer-ygvvo5,
        .framer-zh2Qm .framer-w0ic0i {
            flex: none;
            gap: 0;
            height: 56px;
            position: absolute;
            top: 142px;
            left: 33px;
            right: 33px;
            overflow: visible
        }

        .framer-zh2Qm .framer-dkecyv,
        .framer-zh2Qm .framer-1geiskd {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 101px;
            height: auto;
            position: absolute;
            top: 0;
            left: 116px
        }

        .framer-zh2Qm .framer-k1x5od,
        .framer-zh2Qm .framer-4umn6z {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 101px;
            height: auto;
            position: absolute;
            top: 32px;
            left: 117px
        }

        .framer-zh2Qm .framer-yd54mp,
        .framer-zh2Qm .framer-1s3139n {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 1px;
            --border-right-width: 1px;
            --border-style: solid;
            --border-top-width: 1px;
            background-color: #fff;
            border-radius: 10px;
            flex: none;
            gap: 0;
            height: 231px;
            position: absolute;
            top: 0;
            left: 434px;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-x7tp4j,
        .framer-zh2Qm .framer-qwdgxs {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 80px;
            height: auto;
            position: absolute;
            top: 96px;
            left: 160px
        }

        .framer-zh2Qm .framer-d9oztz,
        .framer-zh2Qm .framer-1jri29e {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: auto;
            height: auto;
            position: absolute;
            top: 141px;
            left: 49%;
            transform: translate(-50%)
        }

        .framer-zh2Qm .framer-49v1ar,
        .framer-zh2Qm .framer-1lvlkw9 {
            flex: none;
            gap: 0;
            height: 40px;
            position: absolute;
            top: 5180px;
            left: 91px;
            right: 1215px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-1bz1o18,
        .framer-zh2Qm .framer-1xgwo57 {
            --border-bottom-width: 1px;
            --border-color: #e8e8e8;
            --border-left-width: 0px;
            --border-right-width: 0px;
            --border-style: solid;
            --border-top-width: 0px;
            background-color: #fff;
            flex: none;
            gap: 0;
            height: 81px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1yqhdq6,
        .framer-zh2Qm .framer-1yk1rjc {
            flex: none;
            gap: 0;
            height: 44px;
            position: absolute;
            top: calc(49.3827% - 22px);
            left: 927px;
            right: 134px;
            overflow: visible
        }

        .framer-zh2Qm .framer-10pfnl9,
        .framer-zh2Qm .framer-19zsy4p {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 56px;
            height: auto;
            position: absolute;
            top: 49%;
            left: 0;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-1m46hnm,
        .framer-zh2Qm .framer-iy60dh {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 82px;
            height: auto;
            position: absolute;
            top: 49%;
            left: 96px;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-11vujvm,
        .framer-zh2Qm .framer-1deoxtr {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 72px;
            height: auto;
            position: absolute;
            top: 49%;
            left: 218px;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-1fex0td,
        .framer-zh2Qm .framer-1cfuw34 {
            background-color: #0244ae;
            border-radius: 8px;
            flex: none;
            gap: 0;
            height: 44px;
            position: absolute;
            top: calc(50% - 22px);
            left: 330px;
            right: 0;
            overflow: visible
        }

        .framer-zh2Qm .framer-1gnnp14,
        .framer-zh2Qm .framer-ieeh4x {
            --framer-paragraph-spacing: 0px;
            white-space: pre;
            flex: none;
            width: 78px;
            height: auto;
            position: absolute;
            top: 49%;
            left: 24px;
            transform: translateY(-50%)
        }

        .framer-zh2Qm .framer-9yy3gh,
        .framer-zh2Qm .framer-1vxirb9 {
            flex: none;
            gap: 0;
            height: 40px;
            position: absolute;
            top: calc(49.3827% - 20px);
            left: 134px;
            right: 1171px;
            overflow: hidden
        }

        .framer-zh2Qm .framer-1po6g6u {
            flex: none;
            height: 629px;
            position: absolute;
            top: 708px;
            left: 0;
            right: 0
        }

        .framer-zh2Qm[data-border=true]:after,
        .framer-zh2Qm [data-border=true]:after {
            content: "";
            border-width: var(--border-top-width, 0)var(--border-right-width, 0)var(--border-bottom-width, 0)var(--border-left-width, 0);
            border-color: var(--border-color, none);
            border-style: var(--border-style, none);
            box-sizing: border-box;
            border-radius: inherit;
            pointer-events: none;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0
        }
    </style><!-- Start of headEnd -->

    <!-- End of headEnd -->
</head>

<body>
    <script async src="https://events.framer.com/script?v=2"
        data-fid="c3dc42fa44f6c51559dab2e9912677acbf8e9bb60fc0f9dfa61054218edaa6ee" data-no-nt></script>
    <!-- Start of bodyStart -->

    <!-- End of bodyStart -->
    <div id="main"
        data-framer-hydrate-v2="{&quot;routeId&quot;:&quot;augiA20Il&quot;,&quot;localeId&quot;:&quot;default&quot;,&quot;breakpoints&quot;:[{&quot;hash&quot;:&quot;72rtr7&quot;}]}"
        data-framer-ssr-released-at="2025-10-09T00:35:18.131Z" data-framer-page-optimized-at="2025-10-09T13:48:05.238Z"
        data-framer-generated-page><!--$-->
        <style data-framer-html-style>
            html body {
                background: rgb(255, 255, 255);
            }
        </style>
        <div data-framer-root class="framer-zh2Qm framer-72rtr7" style="min-height:100vh;width:auto">
            <div class="framer-1ut41i" data-framer-name="Section43 with gradient">
                <div class="framer-1nmh50q" data-framer-name="min-h-screen">
                    <div class="framer-dabyx6" data-framer-name="Section bg-[#fafafa] py-20"></div>
                    <div class="framer-1dpisdd" data-framer-name="Section about"></div>
                    <div class="framer-11nbivf" data-framer-name="Section bg-[#fafafa] py-20"></div>
                    <div class="framer-12pexn1" data-framer-name="Section bg-white py-20"></div>
                    <div class="framer-1tgqfob" data-framer-name="Section projects"></div>
                    <div class="framer-15xdfl1" data-framer-name="Section bg-white py-20"></div>
                    <div class="framer-1f1yors" data-framer-name="Section contact"></div>
                    <div class="framer-1juyrop" data-framer-name="Footer">
                        <div class="framer-1289oy1" data-framer-name="max-w-[1400px] mx-auto px-4 md:px-8">
                            <div class="framer-1gfzmwc"
                                data-framer-name="grid md:grid-cols-[2fr_1fr_1fr] gap-8 md:gap-12 mb-8">
                                <div class="framer-6dh5zk"
                                    data-framer-name="Making property ownership accessible for every Nigerian."
                                    data-framer-component-type="RichTextContainer" style="transform:none">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-line-height:27.36px;--framer-text-color:rgb(153, 153, 153)"
                                        class="framer-text">Making property ownership accessible for every Nigerian.</p>
                                </div>
                                <div class="framer-1s4kr0v" data-framer-name="Frame">
                                    <div class="framer-1xnp0eo" data-framer-name="DISCOVER"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-1px;--framer-line-height:21.6px;--framer-text-color:rgb(255, 255, 255);--framer-text-transform:uppercase"
                                            class="framer-text">DISCOVER</p>
                                    </div>
                                    <div class="framer-eiy48r" data-framer-name="space-y-3">
                                        <div class="framer-1ob80d1" data-framer-name="About Us"
                                            data-framer-component-type="RichTextContainer" style="transform:none">
                                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.01em;--framer-line-height:22.8px;--framer-text-color:rgb(153, 153, 153)"
                                                class="framer-text">About Us</p>
                                        </div>
                                        <div class="framer-156jeac" data-framer-name="Upcoming Projects"
                                            data-framer-component-type="RichTextContainer" style="transform:none">
                                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.01em;--framer-line-height:22.8px;--framer-text-color:rgb(153, 153, 153)"
                                                class="framer-text">Upcoming Projects</p>
                                        </div>
                                        <div class="framer-ky553h" data-framer-name="Contact"
                                            data-framer-component-type="RichTextContainer" style="transform:none">
                                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.01em;--framer-line-height:22.8px;--framer-text-color:rgb(153, 153, 153)"
                                                class="framer-text">Contact</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="framer-13ic58u" data-framer-name="Frame">
                                    <div class="framer-lc7zym" data-framer-name="CONNECT WITH US"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-1px;--framer-line-height:21.6px;--framer-text-color:rgb(255, 255, 255);--framer-text-transform:uppercase"
                                            class="framer-text">CONNECT WITH US</p>
                                    </div>
                                    <div class="framer-zux7mr" data-framer-name="flex gap-3">
                                        <div class="framer-17f5q3k"
                                            data-framer-name="Link w-9 h-9 rounded-full bg-[#333333] flex items-center justify-center hover:bg-[#0244ae] transition text-white">
                                        </div>
                                        <div class="framer-63ogap"
                                            data-framer-name="Link w-9 h-9 rounded-full bg-[#333333] flex items-center justify-center hover:bg-[#0244ae] transition text-white">
                                        </div>
                                        <div class="framer-keri7o"
                                            data-framer-name="Link w-9 h-9 rounded-full bg-[#333333] flex items-center justify-center hover:bg-[#0244ae] transition text-white text-sm">
                                            <div class="framer-1nqshh2" data-framer-name="𝕏"
                                                data-framer-component-type="RichTextContainer" style="transform:none">
                                                <p style="--font-selector:R0Y7U2Vnb2UgVUktcmVndWxhcg==;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:14px;--framer-line-height:20px;--framer-text-color:rgb(255, 255, 255)"
                                                    class="framer-text">𝕏</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-4a0xm0" data-border="true"
                                data-framer-name="border-t border-[#333333] pt-6 text-center">
                                <div class="framer-enn12q" data-framer-name="© 2025 Section43Realty"
                                    data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-0.01em;--framer-line-height:21.6px;--framer-text-color:rgb(153, 153, 153)"
                                        class="framer-text">© 2025 Section43Realty</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-framer-component-type="SVG" data-framer-name="Frame" parentsize="0"
                    _constraints="[object Object]" rotation="0" shadows class="framer-z28w2h" aria-hidden="true"
                    style="image-rendering:pixelated;flex-shrink:0;fill:rgba(0,0,0,1);color:rgba(0,0,0,1)">
                    <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit"><svg
                            style="width:100%;height:100%;" viewBox="0 0 17 17">
                            <use href="#svg1742219479_1214" />
                        </svg></div>
                </div>
                <div data-framer-component-type="SVG" data-framer-name="Frame" parentsize="0"
                    _constraints="[object Object]" rotation="0" shadows class="framer-qmdq6f" aria-hidden="true"
                    style="image-rendering:pixelated;flex-shrink:0;fill:rgba(0,0,0,1);color:rgba(0,0,0,1)">
                    <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit"><svg
                            style="width:100%;height:100%;" viewBox="0 0 17 17">
                            <use href="#svg1548265952_1473" />
                        </svg></div>
                </div>
                <div class="framer-bgxf5h"
                    data-framer-name="Section hero relative min-h-screen overflow-hidden bg-gradient-to-b from-white via-[#f8f9fb] to-[#e8eef5]">
                    <div class="framer-urb4p0" data-framer-name="Rectangle 7"></div>
                    <div class="framer-1g8kjve" data-framer-name="ChatGPT Image Oct 5, 2025, 10_07_22 AM 1">
                        <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                            data-framer-background-image-wrapper="true"><img decoding="async" width="1536" height="1024"
                                sizes="1517px"
                                srcset="https://framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4.png?scale-down-to=512&amp;width=1536&amp;height=1024 512w,https://framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4.png?scale-down-to=1024&amp;width=1536&amp;height=1024 1024w,https://framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4.png?width=1536&amp;height=1024 1536w"
                                src="../framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4c65e.png?width=1536&amp;height=1024"
                                alt
                                style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                        </div>
                    </div>
                    <div class="framer-f1rnyd"
                        data-framer-name="text-center max-w-[900px] mx-auto mb-12 md:mb-16 animate-on-scroll animate-in">
                        <div class="framer-1cw9x66"
                            data-framer-name="text-[2.5rem] sm:text-[3rem] md:text-[4rem] lg:text-[5rem] font-bold leading-[1.1] mb-6 md:mb-8">
                            <div class="framer-3mt1k2" data-framer-name="Land used to be for kings."
                                data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:80px;--framer-letter-spacing:-0.02em;--framer-line-height:88px;--framer-text-alignment:center;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Land used to be for kings.</p>
                            </div>
                            <div class="framer-fqr5u8" data-framer-name="Now it's for anyone with a vision."
                                data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:80px;--framer-letter-spacing:-0.02em;--framer-line-height:88px;--framer-text-alignment:center;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Now it's for anyone with a <span
                                        style="--framer-text-color:rgb(255, 43, 41)" class="framer-text">vision</span>.
                                </p>
                            </div>
                        </div>
                        <div class="framer-n32a2t"
                            data-framer-name="If you can afford an iPhone, you can afford land. Section43Realty is on a mission to make land ownership clear, simple, and accessible."
                            data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-letter-spacing:-0.02em;--framer-line-height:32.5px;--framer-text-alignment:center;--framer-text-color:rgb(74, 85, 104)"
                                class="framer-text">If you can afford an iPhone, you can afford land. Section43Realty is
                                on a mission to make land ownership clear, simple, and accessible.</p>
                        </div>
                        <div class="framer-bj2mjp"
                            data-framer-name="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <div class="framer-1ppn2qm"
                                data-framer-name="Button inline-flex items-center justify-center gap-2 whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive py-2 has-[>svg]:px-3 bg-[#1a1a1a] hover:bg-[#1a1a1a]/90 text-white h-12 md:h-14 px-8 md:px-10 rounded-lg font-semibold text-[0.95rem] md:text-base shadow-lg">
                                <div class="framer-exwksb" data-framer-name="Get Started"
                                    data-framer-component-type="RichTextContainer"
                                    style="transform:translate(-50%, -50%)">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(255, 255, 255)"
                                        class="framer-text">Get Started</p>
                                </div>
                            </div>
                            <div class="framer-k2iu7j" data-border="true"
                                data-framer-name="Button justify-center whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 py-2 has-[>svg]:px-3 bg-transparent hover:bg-white/50 text-[#1a1a1a] h-12 md:h-14 px-6 md:px-8 rounded-lg font-semibold text-[0.95rem] md:text-base border-2 border-[#ff2b29] flex items-center gap-2">
                                <div class="framer-1vl1z9x"
                                    data-framer-name="w-8 h-8 rounded-full bg-[#ff2b29] flex items-center justify-center">
                                </div>
                                <div class="framer-1am15z1" data-framer-name="Watch Video"
                                    data-framer-component-type="RichTextContainer"
                                    style="transform:translate(-50%, -50%)">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                        class="framer-text">Watch Video</p>
                                </div>
                            </div>
                        </div>
                        <div data-framer-component-type="SVG" data-framer-name="Frame" parentsize="0"
                            _constraints="[object Object]" rotation="0" shadows class="framer-kn0a0i" aria-hidden="true"
                            style="image-rendering:pixelated;flex-shrink:0;fill:rgba(0,0,0,1);color:rgba(0,0,0,1)">
                            <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit"><svg
                                    style="width:100%;height:100%;" viewBox="0 0 17 16">
                                    <use href="#svg116991227_313" />
                                </svg></div>
                        </div>
                    </div>
                </div>
                <div class="framer-yex2ii" data-framer-name="max-w-[1400px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-1dz40gf" data-framer-name="Trending properties"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Trending properties</p>
                    </div>
                    <div class="framer-19cvk3g" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden hover:shadow-sm transition-shadow">
                        <div class="framer-vztvj5" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="650px"
                                    srcset="https://framerusercontent.com/images/Aw01t07S0iQcBuKFq452SZV7TGU.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/Aw01t07S0iQcBuKFq452SZV7TGU.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/Aw01t07S0iQcBuKFq452SZV7TGU90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-tk5tb" data-framer-name="p-7">
                            <div class="framer-1wgkbrl" data-framer-name="Lincoln City II"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:22.4px;--framer-letter-spacing:-0.02em;--framer-line-height:33.6px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Lincoln City II</p>
                            </div>
                            <div class="framer-1fix5xc" data-framer-name="Agbowa-Ikosi, Ikorodu Road"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:22.8px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">Agbowa-Ikosi, Ikorodu Road</p>
                            </div>
                            <div class="framer-ldragp"
                                data-framer-name="The land is located in a serene neighborhood, and has everything you need to accommodate a large group."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">The land is located in a serene neighborhood, and has everything
                                    you need to accommodate a large group.</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-1r740jn" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden hover:shadow-sm transition-shadow">
                        <div class="framer-ath030" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="650px"
                                    srcset="https://framerusercontent.com/images/OzHPDG2UKoS4Py6CVGpcxnGp484.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/OzHPDG2UKoS4Py6CVGpcxnGp484.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/OzHPDG2UKoS4Py6CVGpcxnGp48490ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-zkr7g3" data-framer-name="p-7">
                            <div class="framer-cj9ekn" data-framer-name="Green Acres III"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:22.4px;--framer-letter-spacing:-0.02em;--framer-line-height:33.6px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Green Acres III</p>
                            </div>
                            <div class="framer-4y3e26" data-framer-name="Omu-Ketu, Ogun state"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:22.8px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">Omu-Ketu, Ogun state</p>
                            </div>
                            <div class="framer-1wgzqjo"
                                data-framer-name="The land is located in a serene neighborhood, and has everything you need to accommodate a large group."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">The land is located in a serene neighborhood, and has everything
                                    you need to accommodate a large group.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-wa8pnq" data-framer-name="max-w-[1000px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-14hwcki" data-framer-name="About Section43Realty"
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">About Section43Realty</p>
                    </div>
                    <div class="framer-9wsm9z" data-framer-name="grid md:grid-cols-2 gap-12">
                        <div class="framer-15mzms5"
                            data-framer-name="Section43Realty is on a mission to make land ownership in Nigeria transparent, safe, and accessible for everyone. Inspired by Section 43 of the Nigerian Constitution — which guarantees every citizen the right to own property anywhere in the country — we are building a new kind of real estate brand."
                            data-framer-component-type="RichTextContainer" style="transform:none">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:30.24px;--framer-text-color:rgb(102, 102, 102)"
                                class="framer-text">Section43Realty is on a mission to make land ownership in Nigeria
                                transparent, safe, and accessible for everyone. Inspired by Section 43 of the Nigerian
                                Constitution — which guarantees every citizen the right to own property anywhere in the
                                country — we are building a new kind of real estate brand.</p>
                        </div>
                        <div class="framer-1hcvawa"
                            data-framer-name="A brand that stands for trust, simplicity, and community empowerment, not hidden fees, fake documents, or confusion. Whether you're buying your first plot or securing land for your family's future, Section43Realty is here to make the process clear and reliable — every step of the way."
                            data-framer-component-type="RichTextContainer" style="transform:none">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:30.24px;--framer-text-color:rgb(102, 102, 102)"
                                class="framer-text">A brand that stands for trust, simplicity, and community
                                empowerment, not hidden fees, fake documents, or confusion. Whether you're buying your
                                first plot or securing land for your family's future, Section43Realty is here to make
                                the process clear and reliable — every step of the way.</p>
                        </div>
                    </div>
                </div>
                <div class="framer-cei7t6" data-framer-name="max-w-[1400px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-ux665o" data-framer-name="Our Exceptional Values"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Our Exceptional Values</p>
                    </div>
                    <div class="framer-1dbo1au" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden">
                        <div class="framer-o5eqwq" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="422px"
                                    srcset="https://framerusercontent.com/images/zfbRTHteseKR8bl4bVMDXGSNk.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/zfbRTHteseKR8bl4bVMDXGSNk.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/zfbRTHteseKR8bl4bVMDXGSNk90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-kuxeq4" data-framer-name="p-7 text-center">
                            <div class="framer-1q5vahi" data-framer-name="Trust"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20.8px;--framer-letter-spacing:-0.02em;--framer-line-height:31.2px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Trust</p>
                            </div>
                            <div class="framer-1n8yjv3"
                                data-framer-name="No hidden fees, no fake documents. Every transaction is transparent and verified."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">No hidden fees, no fake documents. Every transaction is
                                    transparent and verified.</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-1kh81of" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden">
                        <div class="framer-2ielw0" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="422px"
                                    srcset="https://framerusercontent.com/images/2DWqfm3Libe3mRItH91ggkXGsvE.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/2DWqfm3Libe3mRItH91ggkXGsvE.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/2DWqfm3Libe3mRItH91ggkXGsvE90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-k6mr6x" data-framer-name="p-7 text-center">
                            <div class="framer-1f53pjo" data-framer-name="Simplicity"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20.8px;--framer-letter-spacing:-0.02em;--framer-line-height:31.2px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Simplicity</p>
                            </div>
                            <div class="framer-1f5fay3"
                                data-framer-name="Land ownership should be clear and straightforward, not confusing and complicated."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text"> Land ownership should be clear and straightforward, not
                                    confusing and complicated.</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-1ayrngw" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden">
                        <div class="framer-n7dmha" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="422px"
                                    srcset="https://framerusercontent.com/images/CI1wLWpQSYfn4r5WcWmenXC2s7w.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/CI1wLWpQSYfn4r5WcWmenXC2s7w.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/CI1wLWpQSYfn4r5WcWmenXC2s7w90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-ilm27q" data-framer-name="p-7 text-center">
                            <div class="framer-mfo6dc" data-framer-name="Community"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20.8px;--framer-letter-spacing:-0.02em;--framer-line-height:31.2px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Community</p>
                            </div>
                            <div class="framer-pbg0om"
                                data-framer-name="Empowering every Nigerian to own property and build generational wealth."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">Empowering every Nigerian to own property and build generational
                                    wealth.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-14r6s4k" data-framer-name="max-w-[1400px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-7q5080"
                        data-framer-name="text-[2.5rem] font-bold text-[#1a1a1a] text-center mb-4">
                        <div class="framer-1iec8cg" data-framer-name="Clients spill the beans; their love for us!"
                            data-framer-component-type="RichTextContainer" style="transform:none">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                                class="framer-text">Clients spill the beans; their <span
                                    style="--framer-text-color:rgb(224, 35, 32)" class="framer-text">love</span> for us!
                            </p>
                        </div>
                    </div>
                    <div class="framer-q9f7e8"
                        data-framer-name="Real stories from Nigerians who trusted us with their property dreams"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:25.2px;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">Real stories from Nigerians who trusted us with their property dreams
                        </p>
                    </div>
                    <div class="framer-8uy8fb" data-framer-name="grid md:grid-cols-3 gap-8">
                        <div class="framer-smsd1w" data-border="true"
                            data-framer-name="bg-[#fafafa] rounded-lg border border-[#e8e8e8] p-8">
                            <div class="framer-1nnwkm6" data-framer-name="flex items-center gap-4 mb-4">
                                <div class="framer-1alm3o6"
                                    data-framer-name="w-14 h-14 rounded-full bg-[#0244ae] flex items-center justify-center text-white font-bold text-xl">
                                    <div class="framer-n9j5pl" data-framer-name="C"
                                        data-framer-component-type="RichTextContainer"
                                        style="transform:translateY(-50%)">
                                        <p style="--font-selector:R0Y7U2Vnb2UgVUktNzAw;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-font-weight:700;--framer-line-height:28px;--framer-text-color:rgb(255, 255, 255)"
                                            class="framer-text">C</p>
                                    </div>
                                </div>
                                <div class="framer-16027pc" data-framer-name="Frame">
                                    <div class="framer-ksrljw" data-framer-name="Chioma Okafor"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                            class="framer-text">Chioma Okafor</p>
                                    </div>
                                    <div class="framer-z4c4c6" data-framer-name="Lagos, Nigeria"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:13.6px;--framer-letter-spacing:-0.02em;--framer-line-height:20.4px;--framer-text-color:rgb(102, 102, 102)"
                                            class="framer-text">Lagos, Nigeria</p>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-9qb6vu"
                                data-framer-name="&quot;I bought my first plot of land through Section43Realty. No stress, no hidden charges. Everything was clear from day one. I finally own land!&quot;"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:25.84px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">"I bought my first plot of land through Section43Realty. No
                                    stress, no hidden charges. Everything was clear from day one. I finally own land!"
                                </p>
                            </div>
                        </div>
                        <div class="framer-10w9ehf" data-border="true"
                            data-framer-name="bg-[#fafafa] rounded-lg border border-[#e8e8e8] p-8">
                            <div class="framer-111jwgy" data-framer-name="flex items-center gap-4 mb-4">
                                <div class="framer-uo1fpq"
                                    data-framer-name="w-14 h-14 rounded-full bg-[#ff2b29] flex items-center justify-center text-white font-bold text-xl">
                                    <div class="framer-je6gug" data-framer-name="A"
                                        data-framer-component-type="RichTextContainer"
                                        style="transform:translateY(-50%)">
                                        <p style="--font-selector:R0Y7U2Vnb2UgVUktNzAw;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-font-weight:700;--framer-line-height:28px;--framer-text-color:rgb(255, 255, 255)"
                                            class="framer-text">A</p>
                                    </div>
                                </div>
                                <div class="framer-rduinn" data-framer-name="Frame">
                                    <div class="framer-7ymvx6" data-framer-name="Adebayo Mensah"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                            class="framer-text">Adebayo Mensah</p>
                                    </div>
                                    <div class="framer-1qtdh2w" data-framer-name="Ibadan, Nigeria"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:13.6px;--framer-letter-spacing:-0.02em;--framer-line-height:20.4px;--framer-text-color:rgb(102, 102, 102)"
                                            class="framer-text">Ibadan, Nigeria</p>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-iwvze7"
                                data-framer-name="&quot;As a young professional, I thought land ownership was out of reach. Section43Realty proved me wrong. Transparent process, genuine documents.&quot;"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:25.84px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">"As a young professional, I thought land ownership was out of
                                    reach. Section43Realty proved me wrong. Transparent process, genuine documents."</p>
                            </div>
                        </div>
                        <div class="framer-1qxrfg" data-border="true"
                            data-framer-name="bg-[#fafafa] rounded-lg border border-[#e8e8e8] p-8">
                            <div class="framer-juov0z" data-framer-name="flex items-center gap-4 mb-4">
                                <div class="framer-sfhyq4"
                                    data-framer-name="w-14 h-14 rounded-full bg-[#0244ae] flex items-center justify-center text-white font-bold text-xl">
                                    <div class="framer-oygpat" data-framer-name="F"
                                        data-framer-component-type="RichTextContainer"
                                        style="transform:translateY(-50%)">
                                        <p style="--font-selector:R0Y7U2Vnb2UgVUktNzAw;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-font-weight:700;--framer-line-height:28px;--framer-text-color:rgb(255, 255, 255)"
                                            class="framer-text">F</p>
                                    </div>
                                </div>
                                <div class="framer-11ulwfd" data-framer-name="Frame">
                                    <div class="framer-wcx1m2" data-framer-name="Funmi Adeleke"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                            class="framer-text">Funmi Adeleke</p>
                                    </div>
                                    <div class="framer-kxnwcy" data-framer-name="Ogun State, Nigeria"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:13.6px;--framer-letter-spacing:-0.02em;--framer-line-height:20.4px;--framer-text-color:rgb(102, 102, 102)"
                                            class="framer-text">Ogun State, Nigeria</p>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-1o6ft3b"
                                data-framer-name="&quot;I've dealt with many agents before. Section43Realty is different. They actually care about making property ownership accessible. Highly recommend!&quot;"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:25.84px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">"I've dealt with many agents before. Section43Realty is
                                    different. They actually care about making property ownership accessible. Highly
                                    recommend!"</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-10yfe07"
                    data-framer-name="max-w-[1400px] mx-auto px-8 text-center animate-on-scroll animate-in">
                    <div class="framer-ozsvr8" data-framer-name="Upcoming Projects"
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Upcoming Projects</p>
                    </div>
                    <div class="framer-ealtwa" data-border="true"
                        data-framer-name="inline-block bg-white border border-[#0244ae] text-[#0244ae] px-5 py-2 rounded-md text-[0.9rem] font-semibold mb-6">
                        <div class="framer-1etngah" data-framer-name="🚀 Launching soon"
                            data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-0.02em;--framer-line-height:21.6px;--framer-text-color:rgb(2, 68, 174)"
                                class="framer-text">🚀 Launching soon</p>
                        </div>
                    </div>
                    <div class="framer-jghevh"
                        data-framer-name="We're preparing something special. Stay tuned for our upcoming land projects that will redefine affordable land ownership in Nigeria."
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:30.24px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">We're preparing something special. Stay tuned for our upcoming land
                            projects that will redefine affordable land ownership in Nigeria.</p>
                    </div>
                </div>
                <div class="framer-w9zt0g"
                    data-framer-name="max-w-[1400px] mx-auto px-8 text-center animate-on-scroll animate-in">
                    <div class="framer-j8emao" data-framer-name="Ready to buy a land, or build a custom home?"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Ready to buy a land, or build a custom home?</p>
                    </div>
                    <div class="framer-fkiakh"
                        data-framer-name="Start your journey towards home ownership today and discover the endless possibilities that await you."
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:17.6px;--framer-letter-spacing:-0.02em;--framer-line-height:26.4px;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">Start your journey towards home ownership today and discover the endless
                            possibilities that await you.</p>
                    </div>
                    <div class="framer-iejzl4"
                        data-framer-name="Button inline-flex items-center justify-center gap-2 whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive py-2 has-[>svg]:px-3 bg-[#0244ae] hover:bg-[#0244ae]/90 text-white h-12 px-10 rounded-md font-semibold text-base">
                        <div class="framer-1dwfmnm" data-framer-name="Get Started"
                            data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(255, 255, 255)"
                                class="framer-text">Get Started</p>
                        </div>
                    </div>
                </div>
                <div class="framer-1f923kb" data-framer-name="max-w-[900px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-1po5h49" data-framer-name="Contact us"
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Contact us</p>
                    </div>
                    <div class="framer-17d3mw6" data-framer-name="Let's Make Your Property Ownership Dreams a Reality"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">Let's Make Your Property Ownership Dreams a Reality</p>
                    </div>
                    <div class="framer-1uwoi7l" data-framer-name="grid md:grid-cols-2 gap-8">
                        <div class="framer-1uvfe50" data-border="true"
                            data-framer-name="bg-white rounded-lg border border-[#e8e8e8] p-8 text-center">
                            <div class="framer-13ifh5" data-framer-name="📞"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7U2Vnb2UgVUktcmVndWxhcg==;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:32px;--framer-line-height:48px;--framer-text-color:rgb(10, 10, 10)"
                                    class="framer-text">📞</p>
                            </div>
                            <div class="framer-1uq16m6" data-framer-name="Call Us"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:19.2px;--framer-letter-spacing:-0.02em;--framer-line-height:28.8px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Call Us</p>
                            </div>
                            <div class="framer-ygvvo5" data-framer-name="space-y-2">
                                <div class="framer-dkecyv" data-framer-name="0816 829 2937"
                                    data-framer-component-type="RichTextContainer" style="transform:none">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(2, 68, 174)"
                                        class="framer-text">0816 829 2937</p>
                                </div>
                                <div class="framer-k1x5od" data-framer-name="0806 178 8049"
                                    data-framer-component-type="RichTextContainer" style="transform:none">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(2, 68, 174)"
                                        class="framer-text">0806 178 8049</p>
                                </div>
                            </div>
                        </div>
                        <div class="framer-yd54mp" data-border="true"
                            data-framer-name="bg-white rounded-lg border border-[#e8e8e8] p-8 text-center">
                            <div class="framer-1gc83r" data-framer-name="✉️"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7U2Vnb2UgVUktcmVndWxhcg==;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:32px;--framer-line-height:48px;--framer-text-color:rgb(10, 10, 10)"
                                    class="framer-text">✉️</p>
                            </div>
                            <div class="framer-x7tp4j" data-framer-name="Email Us"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:19.2px;--framer-letter-spacing:-0.02em;--framer-line-height:28.8px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Email Us</p>
                            </div>
                            <div class="framer-d9oztz" data-framer-name="<EMAIL>"
                                data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(2, 68, 174)"
                                    class="framer-text"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-49v1ar" data-framer-name="Image h-10 w-auto brightness-0 invert">
                    <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                        data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy" width="264"
                            height="50"
                            src="../framerusercontent.com/images/Se82HHRklykUDm2rEEhQzOYyRMce07.png?width=264&amp;height=50"
                            alt
                            style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                    </div>
                </div>
                <div class="framer-1bz1o18" data-border="true" data-framer-name="Nav">
                    <div class="framer-1yqhdq6" data-framer-name="hidden md:flex items-center gap-10">
                        <div class="framer-10pfnl9" data-framer-name="ABOUT"
                            data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:0.5px;--framer-line-height:22.8px;--framer-text-color:rgb(26, 26, 26);--framer-text-transform:uppercase"
                                class="framer-text">ABOUT</p>
                        </div>
                        <div class="framer-1m46hnm" data-framer-name="PROJECTS"
                            data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:0.5px;--framer-line-height:22.8px;--framer-text-color:rgb(26, 26, 26);--framer-text-transform:uppercase"
                                class="framer-text">PROJECTS</p>
                        </div>
                        <div class="framer-11vujvm" data-framer-name="CONTACT"
                            data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:0.5px;--framer-line-height:22.8px;--framer-text-color:rgb(26, 26, 26);--framer-text-transform:uppercase"
                                class="framer-text">CONTACT</p>
                        </div>
                        <div class="framer-1fex0td"
                            data-framer-name="Button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive py-2 has-[>svg]:px-3 bg-[#0244ae] hover:bg-[#0244ae]/90 text-white h-11 px-6 rounded-md font-semibold">
                            <div class="framer-1gnnp14" data-framer-name="Get Started"
                                data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14px;--framer-line-height:20px;--framer-text-color:rgb(255, 255, 255)"
                                    class="framer-text">Get Started</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-9yy3gh" data-framer-name="Image h-8 md:h-10 w-auto">
                        <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                            data-framer-background-image-wrapper="true"><img decoding="async" width="264" height="50"
                                src="../framerusercontent.com/images/Se82HHRklykUDm2rEEhQzOYyRMce07.png?width=264&amp;height=50"
                                alt
                                style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                        </div>
                    </div>
                </div>
            </div>
            <div class="framer-emsfbm" data-framer-name="Section43 with gradient">
                <div class="framer-1pcllk4" data-framer-name="min-h-screen">
                    <div class="framer-k9n2df" data-framer-name="Section bg-[#fafafa] py-20"></div>
                    <div class="framer-1jmihuq" data-framer-name="Section about"></div>
                    <div class="framer-1ubyxy8" data-framer-name="Section bg-[#fafafa] py-20"></div>
                    <div class="framer-19blid6" data-framer-name="Section bg-white py-20"></div>
                    <div class="framer-1b15hyq" data-framer-name="Section projects"></div>
                    <div class="framer-1smk3m0" data-framer-name="Section bg-white py-20"></div>
                    <div class="framer-sjfhmj" data-framer-name="Section contact"></div>
                    <div class="framer-rahw6f" data-framer-name="Footer">
                        <div class="framer-1xi6fhc" data-framer-name="max-w-[1400px] mx-auto px-4 md:px-8">
                            <div class="framer-1yzrq8"
                                data-framer-name="grid md:grid-cols-[2fr_1fr_1fr] gap-8 md:gap-12 mb-8">
                                <div class="framer-z2ogxn"
                                    data-framer-name="Making property ownership accessible for every Nigerian."
                                    data-framer-component-type="RichTextContainer" style="transform:none">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-line-height:27.36px;--framer-text-color:rgb(153, 153, 153)"
                                        class="framer-text">Making property ownership accessible for every Nigerian.</p>
                                </div>
                                <div class="framer-9lrf60" data-framer-name="Frame">
                                    <div class="framer-jaeku3" data-framer-name="DISCOVER"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-1px;--framer-line-height:21.6px;--framer-text-color:rgb(255, 255, 255);--framer-text-transform:uppercase"
                                            class="framer-text">DISCOVER</p>
                                    </div>
                                    <div class="framer-zpdqlq" data-framer-name="space-y-3">
                                        <div class="framer-soe31f" data-framer-name="About Us"
                                            data-framer-component-type="RichTextContainer" style="transform:none">
                                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.01em;--framer-line-height:22.8px;--framer-text-color:rgb(153, 153, 153)"
                                                class="framer-text">About Us</p>
                                        </div>
                                        <div class="framer-10emcuz" data-framer-name="Upcoming Projects"
                                            data-framer-component-type="RichTextContainer" style="transform:none">
                                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.01em;--framer-line-height:22.8px;--framer-text-color:rgb(153, 153, 153)"
                                                class="framer-text">Upcoming Projects</p>
                                        </div>
                                        <div class="framer-1dqvloq" data-framer-name="Contact"
                                            data-framer-component-type="RichTextContainer" style="transform:none">
                                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.01em;--framer-line-height:22.8px;--framer-text-color:rgb(153, 153, 153)"
                                                class="framer-text">Contact</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="framer-1017hqs" data-framer-name="Frame">
                                    <div class="framer-i0soma" data-framer-name="CONNECT WITH US"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-1px;--framer-line-height:21.6px;--framer-text-color:rgb(255, 255, 255);--framer-text-transform:uppercase"
                                            class="framer-text">CONNECT WITH US</p>
                                    </div>
                                    <div class="framer-1qprglt" data-framer-name="flex gap-3">
                                        <div class="framer-1gllqq9"
                                            data-framer-name="Link w-9 h-9 rounded-full bg-[#333333] flex items-center justify-center hover:bg-[#0244ae] transition text-white">
                                        </div>
                                        <div class="framer-q5zhuq"
                                            data-framer-name="Link w-9 h-9 rounded-full bg-[#333333] flex items-center justify-center hover:bg-[#0244ae] transition text-white">
                                        </div>
                                        <div class="framer-rlzjdg"
                                            data-framer-name="Link w-9 h-9 rounded-full bg-[#333333] flex items-center justify-center hover:bg-[#0244ae] transition text-white text-sm">
                                            <div class="framer-1rehvw3" data-framer-name="𝕏"
                                                data-framer-component-type="RichTextContainer" style="transform:none">
                                                <p style="--font-selector:R0Y7U2Vnb2UgVUktcmVndWxhcg==;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:14px;--framer-line-height:20px;--framer-text-color:rgb(255, 255, 255)"
                                                    class="framer-text">𝕏</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-14d7byc" data-border="true"
                                data-framer-name="border-t border-[#333333] pt-6 text-center">
                                <div class="framer-1j12pm8" data-framer-name="© 2025 Section43Realty"
                                    data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-0.01em;--framer-line-height:21.6px;--framer-text-color:rgb(153, 153, 153)"
                                        class="framer-text">© 2025 Section43Realty</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-framer-component-type="SVG" data-framer-name="Frame" parentsize="0"
                    _constraints="[object Object]" rotation="0" shadows class="framer-1bocf61" aria-hidden="true"
                    style="image-rendering:pixelated;flex-shrink:0;fill:rgba(0,0,0,1);color:rgba(0,0,0,1)">
                    <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit"><svg
                            style="width:100%;height:100%;" viewBox="0 0 17 17">
                            <use href="#svg1742219479_1214" />
                        </svg></div>
                </div>
                <div data-framer-component-type="SVG" data-framer-name="Frame" parentsize="0"
                    _constraints="[object Object]" rotation="0" shadows class="framer-1gtwy07" aria-hidden="true"
                    style="image-rendering:pixelated;flex-shrink:0;fill:rgba(0,0,0,1);color:rgba(0,0,0,1)">
                    <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit"><svg
                            style="width:100%;height:100%;" viewBox="0 0 17 17">
                            <use href="#svg1548265952_1473" />
                        </svg></div>
                </div>
                <div class="framer-lkf6ti"
                    data-framer-name="Section hero relative min-h-screen overflow-hidden bg-gradient-to-b from-white via-[#f8f9fb] to-[#e8eef5]">
                    <div class="framer-sajmdb" data-framer-name="Rectangle 7"></div>
                    <div class="framer-1po6g6u" data-framer-name="ChatGPT Image Oct 5, 2025, 10_07_22 AM 1">
                        <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                            data-framer-background-image-wrapper="true"><img decoding="async" width="1536" height="1024"
                                sizes="1517px"
                                srcset="https://framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4.png?scale-down-to=512&amp;width=1536&amp;height=1024 512w,https://framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4.png?scale-down-to=1024&amp;width=1536&amp;height=1024 1024w,https://framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4.png?width=1536&amp;height=1024 1536w"
                                src="../framerusercontent.com/images/aNWmw4JsJ2z84v387YvMJuly4c65e.png?width=1536&amp;height=1024"
                                alt
                                style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                        </div>
                    </div>
                    <div class="framer-1nbu5th"
                        data-framer-name="text-center max-w-[900px] mx-auto mb-12 md:mb-16 animate-on-scroll animate-in">
                        <div class="framer-1oo83ln"
                            data-framer-name="text-[2.5rem] sm:text-[3rem] md:text-[4rem] lg:text-[5rem] font-bold leading-[1.1] mb-6 md:mb-8">
                            <div class="framer-16rp71d" data-framer-name="Land used to be for kings."
                                data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:80px;--framer-letter-spacing:-0.02em;--framer-line-height:88px;--framer-text-alignment:center;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Land used to be for kings.</p>
                            </div>
                            <div class="framer-9uwdwr" data-framer-name="Now it's for anyone with a vision."
                                data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:80px;--framer-letter-spacing:-0.02em;--framer-line-height:88px;--framer-text-alignment:center;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Now it's for anyone with a <span
                                        style="--framer-text-color:rgb(255, 43, 41)" class="framer-text">vision</span>.
                                </p>
                            </div>
                        </div>
                        <div class="framer-tp7361"
                            data-framer-name="If you can afford an iPhone, you can afford land. Section43Realty is on a mission to make land ownership clear, simple, and accessible."
                            data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-letter-spacing:-0.02em;--framer-line-height:32.5px;--framer-text-alignment:center;--framer-text-color:rgb(74, 85, 104)"
                                class="framer-text">If you can afford an iPhone, you can afford land. Section43Realty is
                                on a mission to make land ownership clear, simple, and accessible.</p>
                        </div>
                        <div class="framer-1ybfcli"
                            data-framer-name="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <div class="framer-17ab0ds"
                                data-framer-name="Button inline-flex items-center justify-center gap-2 whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive py-2 has-[>svg]:px-3 bg-[#1a1a1a] hover:bg-[#1a1a1a]/90 text-white h-12 md:h-14 px-8 md:px-10 rounded-lg font-semibold text-[0.95rem] md:text-base shadow-lg">
                                <div class="framer-uvsyr5" data-framer-name="Get Started"
                                    data-framer-component-type="RichTextContainer"
                                    style="transform:translate(-50%, -50%)">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(255, 255, 255)"
                                        class="framer-text">Get Started</p>
                                </div>
                            </div>
                            <div class="framer-sktn4y" data-border="true"
                                data-framer-name="Button justify-center whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 py-2 has-[>svg]:px-3 bg-transparent hover:bg-white/50 text-[#1a1a1a] h-12 md:h-14 px-6 md:px-8 rounded-lg font-semibold text-[0.95rem] md:text-base border-2 border-[#ff2b29] flex items-center gap-2">
                                <div class="framer-1c5yww6"
                                    data-framer-name="w-8 h-8 rounded-full bg-[#ff2b29] flex items-center justify-center">
                                </div>
                                <div class="framer-1bz3k2n" data-framer-name="Watch Video"
                                    data-framer-component-type="RichTextContainer"
                                    style="transform:translate(-50%, -50%)">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                        class="framer-text">Watch Video</p>
                                </div>
                            </div>
                        </div>
                        <div data-framer-component-type="SVG" data-framer-name="Frame" parentsize="0"
                            _constraints="[object Object]" rotation="0" shadows class="framer-1rhvh6x"
                            aria-hidden="true"
                            style="image-rendering:pixelated;flex-shrink:0;fill:rgba(0,0,0,1);color:rgba(0,0,0,1)">
                            <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit"><svg
                                    style="width:100%;height:100%;" viewBox="0 0 17 16">
                                    <use href="#svg116991227_313" />
                                </svg></div>
                        </div>
                    </div>
                </div>
                <div class="framer-61nycl" data-framer-name="max-w-[1400px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-1yoc00u" data-framer-name="Trending properties"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Trending properties</p>
                    </div>
                    <div class="framer-16ldven" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden hover:shadow-sm transition-shadow">
                        <div class="framer-pyyw4m" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="650px"
                                    srcset="https://framerusercontent.com/images/Aw01t07S0iQcBuKFq452SZV7TGU.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/Aw01t07S0iQcBuKFq452SZV7TGU.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/Aw01t07S0iQcBuKFq452SZV7TGU90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-8v2yyj" data-framer-name="p-7">
                            <div class="framer-z2741a" data-framer-name="Lincoln City II"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:22.4px;--framer-letter-spacing:-0.02em;--framer-line-height:33.6px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Lincoln City II</p>
                            </div>
                            <div class="framer-1fc9t6i" data-framer-name="Agbowa-Ikosi, Ikorodu Road"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:22.8px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">Agbowa-Ikosi, Ikorodu Road</p>
                            </div>
                            <div class="framer-1cw171m"
                                data-framer-name="The land is located in a serene neighborhood, and has everything you need to accommodate a large group."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">The land is located in a serene neighborhood, and has everything
                                    you need to accommodate a large group.</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-1e6fgsh" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden hover:shadow-sm transition-shadow">
                        <div class="framer-14gsmt4" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="650px"
                                    srcset="https://framerusercontent.com/images/OzHPDG2UKoS4Py6CVGpcxnGp484.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/OzHPDG2UKoS4Py6CVGpcxnGp484.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/OzHPDG2UKoS4Py6CVGpcxnGp48490ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-9q1tw1" data-framer-name="p-7">
                            <div class="framer-1188d38" data-framer-name="Green Acres III"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:22.4px;--framer-letter-spacing:-0.02em;--framer-line-height:33.6px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Green Acres III</p>
                            </div>
                            <div class="framer-13w21p4" data-framer-name="Omu-Ketu, Ogun state"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:22.8px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">Omu-Ketu, Ogun state</p>
                            </div>
                            <div class="framer-1md83vf"
                                data-framer-name="The land is located in a serene neighborhood, and has everything you need to accommodate a large group."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">The land is located in a serene neighborhood, and has everything
                                    you need to accommodate a large group.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-1c18fdf" data-framer-name="max-w-[1000px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-1kul0ly" data-framer-name="About Section43Realty"
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">About Section43Realty</p>
                    </div>
                    <div class="framer-m8gayt" data-framer-name="grid md:grid-cols-2 gap-12">
                        <div class="framer-6wlq37"
                            data-framer-name="Section43Realty is on a mission to make land ownership in Nigeria transparent, safe, and accessible for everyone. Inspired by Section 43 of the Nigerian Constitution — which guarantees every citizen the right to own property anywhere in the country — we are building a new kind of real estate brand."
                            data-framer-component-type="RichTextContainer" style="transform:none">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:30.24px;--framer-text-color:rgb(102, 102, 102)"
                                class="framer-text">Section43Realty is on a mission to make land ownership in Nigeria
                                transparent, safe, and accessible for everyone. Inspired by Section 43 of the Nigerian
                                Constitution — which guarantees every citizen the right to own property anywhere in the
                                country — we are building a new kind of real estate brand.</p>
                        </div>
                        <div class="framer-u6k9il"
                            data-framer-name="A brand that stands for trust, simplicity, and community empowerment, not hidden fees, fake documents, or confusion. Whether you're buying your first plot or securing land for your family's future, Section43Realty is here to make the process clear and reliable — every step of the way."
                            data-framer-component-type="RichTextContainer" style="transform:none">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:30.24px;--framer-text-color:rgb(102, 102, 102)"
                                class="framer-text">A brand that stands for trust, simplicity, and community
                                empowerment, not hidden fees, fake documents, or confusion. Whether you're buying your
                                first plot or securing land for your family's future, Section43Realty is here to make
                                the process clear and reliable — every step of the way.</p>
                        </div>
                    </div>
                </div>
                <div class="framer-ce7oq4" data-framer-name="max-w-[1400px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-fxooaf" data-framer-name="Our Exceptional Values"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Our Exceptional Values</p>
                    </div>
                    <div class="framer-f19z" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden">
                        <div class="framer-1it48r1" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="422px"
                                    srcset="https://framerusercontent.com/images/zfbRTHteseKR8bl4bVMDXGSNk.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/zfbRTHteseKR8bl4bVMDXGSNk.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/zfbRTHteseKR8bl4bVMDXGSNk90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-g2fb20" data-framer-name="p-7 text-center">
                            <div class="framer-1a77hvy" data-framer-name="Trust"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20.8px;--framer-letter-spacing:-0.02em;--framer-line-height:31.2px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Trust</p>
                            </div>
                            <div class="framer-xrppr8"
                                data-framer-name="No hidden fees, no fake documents. Every transaction is transparent and verified."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">No hidden fees, no fake documents. Every transaction is
                                    transparent and verified.</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-16rttp2" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden">
                        <div class="framer-1ctyu9q" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="422px"
                                    srcset="https://framerusercontent.com/images/2DWqfm3Libe3mRItH91ggkXGsvE.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/2DWqfm3Libe3mRItH91ggkXGsvE.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/2DWqfm3Libe3mRItH91ggkXGsvE90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-ntm4ju" data-framer-name="p-7 text-center">
                            <div class="framer-26cqmb" data-framer-name="Simplicity"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20.8px;--framer-letter-spacing:-0.02em;--framer-line-height:31.2px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Simplicity</p>
                            </div>
                            <div class="framer-otnao8"
                                data-framer-name="Land ownership should be clear and straightforward, not confusing and complicated."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text"> Land ownership should be clear and straightforward, not
                                    confusing and complicated.</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-1bzvda2" data-border="true"
                        data-framer-name="bg-white rounded-lg border border-[#e8e8e8] overflow-hidden">
                        <div class="framer-11rjkpd" data-framer-name="Image w-full h-full object-cover">
                            <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                                data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy"
                                    width="1024" height="1024" sizes="422px"
                                    srcset="https://framerusercontent.com/images/CI1wLWpQSYfn4r5WcWmenXC2s7w.png?scale-down-to=512&amp;width=1024&amp;height=1024 512w,https://framerusercontent.com/images/CI1wLWpQSYfn4r5WcWmenXC2s7w.png?width=1024&amp;height=1024 1024w"
                                    src="../framerusercontent.com/images/CI1wLWpQSYfn4r5WcWmenXC2s7w90ee.png?width=1024&amp;height=1024"
                                    alt
                                    style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                            </div>
                        </div>
                        <div class="framer-db7yfz" data-framer-name="p-7 text-center">
                            <div class="framer-1hstmqn" data-framer-name="Community"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:20.8px;--framer-letter-spacing:-0.02em;--framer-line-height:31.2px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Community</p>
                            </div>
                            <div class="framer-16qv9ev"
                                data-framer-name="Empowering every Nigerian to own property and build generational wealth."
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:24.32px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">Empowering every Nigerian to own property and build generational
                                    wealth.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-1vr9t0" data-framer-name="max-w-[1400px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-1bmqcxv"
                        data-framer-name="text-[2.5rem] font-bold text-[#1a1a1a] text-center mb-4">
                        <div class="framer-mk030y" data-framer-name="Clients spill the beans; their love for us!"
                            data-framer-component-type="RichTextContainer" style="transform:none">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                                class="framer-text">Clients spill the beans; their <span
                                    style="--framer-text-color:rgb(224, 35, 32)" class="framer-text">love</span> for us!
                            </p>
                        </div>
                    </div>
                    <div class="framer-ozyjis"
                        data-framer-name="Real stories from Nigerians who trusted us with their property dreams"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:25.2px;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">Real stories from Nigerians who trusted us with their property dreams
                        </p>
                    </div>
                    <div class="framer-jbfc65" data-framer-name="grid md:grid-cols-3 gap-8">
                        <div class="framer-b5jtqg" data-border="true"
                            data-framer-name="bg-[#fafafa] rounded-lg border border-[#e8e8e8] p-8">
                            <div class="framer-1rw71r9" data-framer-name="flex items-center gap-4 mb-4">
                                <div class="framer-ii1a9e"
                                    data-framer-name="w-14 h-14 rounded-full bg-[#0244ae] flex items-center justify-center text-white font-bold text-xl">
                                    <div class="framer-tr5wjj" data-framer-name="C"
                                        data-framer-component-type="RichTextContainer"
                                        style="transform:translateY(-50%)">
                                        <p style="--font-selector:R0Y7U2Vnb2UgVUktNzAw;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-font-weight:700;--framer-line-height:28px;--framer-text-color:rgb(255, 255, 255)"
                                            class="framer-text">C</p>
                                    </div>
                                </div>
                                <div class="framer-1r1qnsl" data-framer-name="Frame">
                                    <div class="framer-ko53z0" data-framer-name="Chioma Okafor"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                            class="framer-text">Chioma Okafor</p>
                                    </div>
                                    <div class="framer-p8zwjy" data-framer-name="Lagos, Nigeria"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:13.6px;--framer-letter-spacing:-0.02em;--framer-line-height:20.4px;--framer-text-color:rgb(102, 102, 102)"
                                            class="framer-text">Lagos, Nigeria</p>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-co3opv"
                                data-framer-name="&quot;I bought my first plot of land through Section43Realty. No stress, no hidden charges. Everything was clear from day one. I finally own land!&quot;"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:25.84px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">"I bought my first plot of land through Section43Realty. No
                                    stress, no hidden charges. Everything was clear from day one. I finally own land!"
                                </p>
                            </div>
                        </div>
                        <div class="framer-1blb1af" data-border="true"
                            data-framer-name="bg-[#fafafa] rounded-lg border border-[#e8e8e8] p-8">
                            <div class="framer-1qhvu" data-framer-name="flex items-center gap-4 mb-4">
                                <div class="framer-wnv9ck"
                                    data-framer-name="w-14 h-14 rounded-full bg-[#ff2b29] flex items-center justify-center text-white font-bold text-xl">
                                    <div class="framer-17qkmiy" data-framer-name="A"
                                        data-framer-component-type="RichTextContainer"
                                        style="transform:translateY(-50%)">
                                        <p style="--font-selector:R0Y7U2Vnb2UgVUktNzAw;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-font-weight:700;--framer-line-height:28px;--framer-text-color:rgb(255, 255, 255)"
                                            class="framer-text">A</p>
                                    </div>
                                </div>
                                <div class="framer-1cywl9b" data-framer-name="Frame">
                                    <div class="framer-l9v8xu" data-framer-name="Adebayo Mensah"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                            class="framer-text">Adebayo Mensah</p>
                                    </div>
                                    <div class="framer-dbomlg" data-framer-name="Ibadan, Nigeria"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:13.6px;--framer-letter-spacing:-0.02em;--framer-line-height:20.4px;--framer-text-color:rgb(102, 102, 102)"
                                            class="framer-text">Ibadan, Nigeria</p>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-heu1cu"
                                data-framer-name="&quot;As a young professional, I thought land ownership was out of reach. Section43Realty proved me wrong. Transparent process, genuine documents.&quot;"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:25.84px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">"As a young professional, I thought land ownership was out of
                                    reach. Section43Realty proved me wrong. Transparent process, genuine documents."</p>
                            </div>
                        </div>
                        <div class="framer-9ta0y6" data-border="true"
                            data-framer-name="bg-[#fafafa] rounded-lg border border-[#e8e8e8] p-8">
                            <div class="framer-1bsxeae" data-framer-name="flex items-center gap-4 mb-4">
                                <div class="framer-jceoua"
                                    data-framer-name="w-14 h-14 rounded-full bg-[#0244ae] flex items-center justify-center text-white font-bold text-xl">
                                    <div class="framer-r8ovew" data-framer-name="F"
                                        data-framer-component-type="RichTextContainer"
                                        style="transform:translateY(-50%)">
                                        <p style="--font-selector:R0Y7U2Vnb2UgVUktNzAw;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:20px;--framer-font-weight:700;--framer-line-height:28px;--framer-text-color:rgb(255, 255, 255)"
                                            class="framer-text">F</p>
                                    </div>
                                </div>
                                <div class="framer-1yoh7by" data-framer-name="Frame">
                                    <div class="framer-1l1k1is" data-framer-name="Funmi Adeleke"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(26, 26, 26)"
                                            class="framer-text">Funmi Adeleke</p>
                                    </div>
                                    <div class="framer-1os0mrq" data-framer-name="Ogun State, Nigeria"
                                        data-framer-component-type="RichTextContainer" style="transform:none">
                                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:13.6px;--framer-letter-spacing:-0.02em;--framer-line-height:20.4px;--framer-text-color:rgb(102, 102, 102)"
                                            class="framer-text">Ogun State, Nigeria</p>
                                    </div>
                                </div>
                            </div>
                            <div class="framer-aemnh2"
                                data-framer-name="&quot;I've dealt with many agents before. Section43Realty is different. They actually care about making property ownership accessible. Highly recommend!&quot;"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:-0.02em;--framer-line-height:25.84px;--framer-text-color:rgb(102, 102, 102)"
                                    class="framer-text">"I've dealt with many agents before. Section43Realty is
                                    different. They actually care about making property ownership accessible. Highly
                                    recommend!"</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-voizp4"
                    data-framer-name="max-w-[1400px] mx-auto px-8 text-center animate-on-scroll animate-in">
                    <div class="framer-4gjnmf" data-framer-name="Upcoming Projects"
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Upcoming Projects</p>
                    </div>
                    <div class="framer-s1ajnp" data-border="true"
                        data-framer-name="inline-block bg-white border border-[#0244ae] text-[#0244ae] px-5 py-2 rounded-md text-[0.9rem] font-semibold mb-6">
                        <div class="framer-qxlxlw" data-framer-name="🚀 Launching soon"
                            data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14.4px;--framer-letter-spacing:-0.02em;--framer-line-height:21.6px;--framer-text-color:rgb(2, 68, 174)"
                                class="framer-text">🚀 Launching soon</p>
                        </div>
                    </div>
                    <div class="framer-1ggorj7"
                        data-framer-name="We're preparing something special. Stay tuned for our upcoming land projects that will redefine affordable land ownership in Nigeria."
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:16.8px;--framer-letter-spacing:-0.02em;--framer-line-height:30.24px;--framer-text-alignment:center;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">We're preparing something special. Stay tuned for our upcoming land
                            projects that will redefine affordable land ownership in Nigeria.</p>
                    </div>
                </div>
                <div class="framer-oqprjs"
                    data-framer-name="max-w-[1400px] mx-auto px-8 text-center animate-on-scroll animate-in">
                    <div class="framer-3kne09" data-framer-name="Ready to buy a land, or build a custom home?"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Ready to buy a land, or build a custom home?</p>
                    </div>
                    <div class="framer-gtl63p"
                        data-framer-name="Start your journey towards home ownership today and discover the endless possibilities that await you."
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:17.6px;--framer-letter-spacing:-0.02em;--framer-line-height:26.4px;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">Start your journey towards home ownership today and discover the endless
                            possibilities that await you.</p>
                    </div>
                    <div class="framer-1v2n5ps"
                        data-framer-name="Button inline-flex items-center justify-center gap-2 whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive py-2 has-[>svg]:px-3 bg-[#0244ae] hover:bg-[#0244ae]/90 text-white h-12 px-10 rounded-md font-semibold text-base">
                        <div class="framer-1t3jlbl" data-framer-name="Get Started"
                            data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(255, 255, 255)"
                                class="framer-text">Get Started</p>
                        </div>
                    </div>
                </div>
                <div class="framer-1v8qv82" data-framer-name="max-w-[900px] mx-auto px-8 animate-on-scroll animate-in">
                    <div class="framer-1pwm5vk" data-framer-name="Contact us"
                        data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:40px;--framer-letter-spacing:-0.02em;--framer-line-height:60px;--framer-text-color:rgb(26, 26, 26)"
                            class="framer-text">Contact us</p>
                    </div>
                    <div class="framer-1hubfum" data-framer-name="Let's Make Your Property Ownership Dreams a Reality"
                        data-framer-component-type="RichTextContainer" style="transform:none">
                        <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(102, 102, 102)"
                            class="framer-text">Let's Make Your Property Ownership Dreams a Reality</p>
                    </div>
                    <div class="framer-16my1ex" data-framer-name="grid md:grid-cols-2 gap-8">
                        <div class="framer-1pa78gp" data-border="true"
                            data-framer-name="bg-white rounded-lg border border-[#e8e8e8] p-8 text-center">
                            <div class="framer-2jlx2z" data-framer-name="📞"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7U2Vnb2UgVUktcmVndWxhcg==;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:32px;--framer-line-height:48px;--framer-text-color:rgb(10, 10, 10)"
                                    class="framer-text">📞</p>
                            </div>
                            <div class="framer-jb5ixq" data-framer-name="Call Us"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:19.2px;--framer-letter-spacing:-0.02em;--framer-line-height:28.8px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Call Us</p>
                            </div>
                            <div class="framer-w0ic0i" data-framer-name="space-y-2">
                                <div class="framer-1geiskd" data-framer-name="0816 829 2937"
                                    data-framer-component-type="RichTextContainer" style="transform:none">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(2, 68, 174)"
                                        class="framer-text">0816 829 2937</p>
                                </div>
                                <div class="framer-4umn6z" data-framer-name="0806 178 8049"
                                    data-framer-component-type="RichTextContainer" style="transform:none">
                                    <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(2, 68, 174)"
                                        class="framer-text">0806 178 8049</p>
                                </div>
                            </div>
                        </div>
                        <div class="framer-1s3139n" data-border="true"
                            data-framer-name="bg-white rounded-lg border border-[#e8e8e8] p-8 text-center">
                            <div class="framer-1vyacqz" data-framer-name="✉️"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7U2Vnb2UgVUktcmVndWxhcg==;--framer-font-family:&quot;Segoe UI&quot;, &quot;Segoe UI Placeholder&quot;, sans-serif;--framer-font-size:32px;--framer-line-height:48px;--framer-text-color:rgb(10, 10, 10)"
                                    class="framer-text">✉️</p>
                            </div>
                            <div class="framer-qwdgxs" data-framer-name="Email Us"
                                data-framer-component-type="RichTextContainer" style="transform:none">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:19.2px;--framer-letter-spacing:-0.02em;--framer-line-height:28.8px;--framer-text-color:rgb(26, 26, 26)"
                                    class="framer-text">Email Us</p>
                            </div>
                            <div class="framer-1jri29e" data-framer-name="<EMAIL>"
                                data-framer-component-type="RichTextContainer" style="transform:translateX(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-letter-spacing:-0.02em;--framer-line-height:24px;--framer-text-color:rgb(2, 68, 174)"
                                    class="framer-text"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="framer-1lvlkw9" data-framer-name="Image h-10 w-auto brightness-0 invert">
                    <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                        data-framer-background-image-wrapper="true"><img decoding="async" loading="lazy" width="264"
                            height="50"
                            src="../framerusercontent.com/images/Se82HHRklykUDm2rEEhQzOYyRMce07.png?width=264&amp;height=50"
                            alt
                            style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                    </div>
                </div>
                <div class="framer-1xgwo57" data-border="true" data-framer-name="Nav">
                    <div class="framer-1yk1rjc" data-framer-name="hidden md:flex items-center gap-10">
                        <div class="framer-19zsy4p" data-framer-name="ABOUT"
                            data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:0.5px;--framer-line-height:22.8px;--framer-text-color:rgb(26, 26, 26);--framer-text-transform:uppercase"
                                class="framer-text">ABOUT</p>
                        </div>
                        <div class="framer-iy60dh" data-framer-name="PROJECTS"
                            data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:0.5px;--framer-line-height:22.8px;--framer-text-color:rgb(26, 26, 26);--framer-text-transform:uppercase"
                                class="framer-text">PROJECTS</p>
                        </div>
                        <div class="framer-1deoxtr" data-framer-name="CONTACT"
                            data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                            <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:15.2px;--framer-letter-spacing:0.5px;--framer-line-height:22.8px;--framer-text-color:rgb(26, 26, 26);--framer-text-transform:uppercase"
                                class="framer-text">CONTACT</p>
                        </div>
                        <div class="framer-1cfuw34"
                            data-framer-name="Button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*='size-'])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive py-2 has-[>svg]:px-3 bg-[#0244ae] hover:bg-[#0244ae]/90 text-white h-11 px-6 rounded-md font-semibold">
                            <div class="framer-ieeh4x" data-framer-name="Get Started"
                                data-framer-component-type="RichTextContainer" style="transform:translateY(-50%)">
                                <p style="--font-selector:R0Y7SGVybyBMaWdodC1yZWd1bGFy;--framer-font-family:&quot;Hero Light&quot;, &quot;Hero Light Placeholder&quot;, sans-serif;--framer-font-size:14px;--framer-line-height:20px;--framer-text-color:rgb(255, 255, 255)"
                                    class="framer-text">Get Started</p>
                            </div>
                        </div>
                    </div>
                    <div class="framer-1vxirb9" data-framer-name="Image h-8 md:h-10 w-auto">
                        <div style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0"
                            data-framer-background-image-wrapper="true"><img decoding="async" width="264" height="50"
                                src="../framerusercontent.com/images/Se82HHRklykUDm2rEEhQzOYyRMce07.png?width=264&amp;height=50"
                                alt
                                style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="overlay"></div><!--/$-->
    </div>
    <script>(() => { function u() { function n(t, e, i) { let r = document.createElement("a"); r.href = t, r.target = i, r.rel = e, document.body.appendChild(r), r.click(), r.remove() } function o(t) { if (this.dataset.hydrated) { this.removeEventListener("click", o); return } t.preventDefault(), t.stopPropagation(); let e = this.getAttribute("href"); if (!e) return; if (/Mac|iPod|iPhone|iPad/u.test(navigator.userAgent) ? t.metaKey : t.ctrlKey) return n(e, "", "_blank"); let r = this.getAttribute("rel") ?? "", c = this.getAttribute("target") ?? ""; n(e, r, c) } function a(t) { if (this.dataset.hydrated) { this.removeEventListener("auxclick", o); return } t.preventDefault(), t.stopPropagation(); let e = this.getAttribute("href"); e && n(e, "", "_blank") } function s(t) { if (this.dataset.hydrated) { this.removeEventListener("keydown", s); return } if (t.key !== "Enter") return; t.preventDefault(), t.stopPropagation(); let e = this.getAttribute("href"); if (!e) return; let i = this.getAttribute("rel") ?? "", r = this.getAttribute("target") ?? ""; n(e, i, r) } document.querySelectorAll("[data-nested-link]").forEach(t => { t instanceof HTMLElement && (t.addEventListener("click", o), t.addEventListener("auxclick", a), t.addEventListener("keydown", s)) }) } return u })()()</script>
    <script>(() => { function i() { for (let e of document.querySelectorAll("[data-framer-original-sizes]")) { let t = e.getAttribute("data-framer-original-sizes"); t === "" ? e.removeAttribute("sizes") : e.setAttribute("sizes", t), e.removeAttribute("data-framer-original-sizes") } } function a() { window.__framer_onRewriteBreakpoints = i } return a })()()</script>
    <script>!function () {
            var w = "framer_variant"; function u(a, r) { let n = r.indexOf("#"), t = n === -1 ? r : r.substring(0, n), i = n === -1 ? "" : r.substring(n), e = t.indexOf("?"), h = e === -1 ? t : t.substring(0, e), d = e === -1 ? "" : t.substring(e), s = new URLSearchParams(d), g = new URLSearchParams(a); for (let [o, l] of g) s.has(o) || o !== w && s.append(o, l); let c = s.toString(); return c === "" ? t + i : h + "?" + c + i } var p = 'div#main a[href^="#"],div#main a[href^="/"],div#main a[href^="."]', f = "div#main a[data-framer-preserve-params]", m, S = (m = document.currentScript) == null ? void 0 : m.hasAttribute("data-preserve-internal-params"); if (window.location.search && !/bot|-google|google-|yandex|ia_archiver|crawl|spider/iu.test(navigator.userAgent)) { let a = document.querySelectorAll(S ? `${p},${f}` : f); for (let r of a) { let n = u(window.location.search, r.href); r.setAttribute("href", n) } }
        }()</script>

    <div id="__framer-badge-container"><!--$--><!--$--><!--$--><a
            class="framer-6jWyo framer-n0ccwk framer-v-n0ccwk framer-bmpgw8 __framer-badge"
            data-framer-appear-id="n0ccwk" data-framer-name="Light" data-nosnippet="true"
            style="will-change:transform;pointer-events:auto;opacity:0.001;transform:translateY(10px)"
            href="https://www.framer.com/" rel="noopener"
            title="Create a free website with Framer, the website builder loved by startups, designers and agencies.">
            <div class="framer-13yxzio" data-framer-name="Backdrop"
                style="background-color:rgb(255, 255, 255);border-bottom-left-radius:10px;border-bottom-right-radius:10px;border-top-left-radius:10px;border-top-right-radius:10px;box-shadow:0px 0.6021873017743928px 1.5656869846134214px -1.5px rgba(0, 0, 0, 0.17), 0px 2.288533303243457px 5.950186588432988px -3px rgba(0, 0, 0, 0.14), 0px 10px 26px -4.5px rgba(0, 0, 0, 0.02)">
            </div>
            <div class="framer-19yaanm" data-framer-name="Content" style="transform:translate(-50%, -50%)">
                <div class="framer-1kflzx5">
                    <div data-framer-name="Logo" class="framer-hcsc7 framer-e50co"
                        style="--1bd4d3i:rgb(0, 0, 0);--otdjsv:rgb(0, 0, 0);transform:translateX(-50%)"></div>
                </div><!--$-->
                <p style="position:absolute;transform:scale(0.001)">Create a free website with Framer, the website
                    builder loved by startups, designers and agencies.</p>
                <div data-framer-name="Text" class="framer-g7oZR framer-1um7t9d"
                    style="--1bd4d3i:rgb(0, 0, 0);--otdjsv:rgb(0, 0, 0)"></div><!--/$-->
            </div>
            <div class="framer-j4ugry" data-framer-name="Bottom"
                style="mask:linear-gradient(180deg, rgba(0,0,0,0) 65%, rgba(0,0,0,1) 100%) add;-webkit-mask:linear-gradient(180deg, rgba(0,0,0,0) 65%, rgba(0,0,0,1) 100%) add;border-bottom-left-radius:11px;border-bottom-right-radius:11px;border-top-left-radius:11px;border-top-right-radius:11px;box-shadow:inset 0px 0px 0px 1px rgb(0, 0, 0);opacity:0.06">
            </div>
            <div class="framer-jnuwbw" data-framer-name="Border"
                style="border-bottom-left-radius:11px;border-bottom-right-radius:11px;border-top-left-radius:11px;border-top-right-radius:11px;box-shadow:inset 0px 0px 0px 1px rgb(0, 0, 0);opacity:0.04">
            </div>
        </a><!--/$--><!--/$--><!--/$--></div>
    <script data-framer-appear-animation="no-preference"></script>
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/react.DP3J7VyN.mjs">
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/rolldown-runtime.ao81ExY3.mjs">
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/framer.6Lt6A2z2.mjs">
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/motion.bYenu_6a.mjs">
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/xzG7chYOtIQcz1vTx8e0ti4zSebGils8jLTYN8Rdu8M.DHZ0kxn7.mjs">
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/shared-lib.C_NyCdNK.mjs">
    <link rel="modulepreload" fetchpriority="low"
        href="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/script_main.BfdcEj5i.mjs">
    <script type="module" async data-framer-bundle="main" fetchpriority="low"
        src="https://framerusercontent.com/sites/51eXBI7VhwROYfrLGYQH7I/script_main.BfdcEj5i.mjs"></script>
    <div id="svg-templates"
        style="position: absolute; overflow: hidden; bottom: 0; left: 0; width: 0; height: 0; z-index: 0; contain: strict"
        aria-hidden="true">
        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" id="svg1742219479_1214">
            <path
                d="M11.167 6.02075C12.2279 6.02075 13.2453 6.44218 13.9954 7.19232C14.7456 7.94247 15.167 8.95989 15.167 10.0208V14.6874H12.5003V10.0208C12.5003 9.66713 12.3598 9.32799 12.1098 9.07794C11.8598 8.82789 11.5206 8.68742 11.167 8.68742C10.8134 8.68742 10.4742 8.82789 10.2242 9.07794C9.97413 9.32799 9.83366 9.66713 9.83366 10.0208V14.6874H7.16699V10.0208C7.16699 8.95989 7.58842 7.94247 8.33857 7.19232C9.08871 6.44218 10.1061 6.02075 11.167 6.02075Z"
                stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.33333" stroke-linecap="round"
                stroke-linejoin="round" />
            <path d="M4.49967 6.6875H1.83301V14.6875H4.49967V6.6875Z" stroke="white"
                style="stroke:white;stroke-opacity:1;" stroke-width="1.33333" stroke-linecap="round"
                stroke-linejoin="round" />
            <path
                d="M3.16634 4.68742C3.90272 4.68742 4.49967 4.09047 4.49967 3.35409C4.49967 2.61771 3.90272 2.02075 3.16634 2.02075C2.42996 2.02075 1.83301 2.61771 1.83301 3.35409C1.83301 4.09047 2.42996 4.68742 3.16634 4.68742Z"
                stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.33333" stroke-linecap="round"
                stroke-linejoin="round" />
        </svg>
        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" id="svg1548265952_1473">
            <g clip-path="url(#svg1548265952_1473_clip0_39_117)">
                <path
                    d="M11.833 2.02075H5.16634C3.32539 2.02075 1.83301 3.51314 1.83301 5.35408V12.0208C1.83301 13.8617 3.32539 15.3541 5.16634 15.3541H11.833C13.674 15.3541 15.1663 13.8617 15.1663 12.0208V5.35408C15.1663 3.51314 13.674 2.02075 11.833 2.02075Z"
                    stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.33333" stroke-linecap="round"
                    stroke-linejoin="round" />
                <path
                    d="M11.1668 8.26746C11.2491 8.82229 11.1543 9.38893 10.896 9.8868C10.6376 10.3847 10.2289 10.7884 9.72788 11.0406C9.22687 11.2927 8.6591 11.3805 8.10532 11.2914C7.55155 11.2023 7.03997 10.9408 6.64336 10.5442C6.24674 10.1476 5.98528 9.63604 5.89618 9.08227C5.80707 8.52849 5.89484 7.96072 6.14701 7.45971C6.39919 6.95869 6.80292 6.54995 7.30079 6.29162C7.79865 6.03329 8.3653 5.93852 8.92013 6.02079C9.48608 6.10471 10.01 6.36843 10.4146 6.77299C10.8192 7.17756 11.0829 7.70151 11.1668 8.26746Z"
                    stroke="white" style="stroke:white;stroke-opacity:1;" stroke-width="1.33333" stroke-linecap="round"
                    stroke-linejoin="round" />
                <path d="M12.167 5.02075H12.1737" stroke="white" style="stroke:white;stroke-opacity:1;"
                    stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round" />
            </g>
            <defs>
                <clipPath id="svg1548265952_1473_clip0_39_117">
                    <rect width="16" height="16" fill="white" style="fill:white;fill-opacity:1;"
                        transform="translate(0.5 0.6875)" />
                </clipPath>
            </defs>
        </svg>
        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" id="svg116991227_313">
            <path d="M4.45312 2L13.7865 8L4.45312 14V2Z" fill="white" stroke="white"
                style="fill:white;fill-opacity:1;stroke:white;stroke-opacity:1;" stroke-width="1.33333"
                stroke-linecap="round" stroke-linejoin="round" />
        </svg>
    </div>
    <!-- Start of bodyEnd -->

    <!-- End of bodyEnd -->
</body>

<!-- Mirrored from section43realty.framer.website/ by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 09 Oct 2025 19:33:05 GMT -->

</html>